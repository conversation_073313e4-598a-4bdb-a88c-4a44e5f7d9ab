<div class="overflow-hidden bg-white shadow-xs sm:rounded-lg">
  <div class="flex justify-between">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-base font-semibold leading-6 text-gray-900">Transcribe</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">Load mailers and enter form data.</p>
    </div>
    <div class="flex px-4 py-4 sm:px-6">
      <.form for={@session_form} phx-change="change_session" class="flex px-3 pr-4">
        <div class="m-auto px-3 text-sm text-zinc-500 font-light">Session Type:</div>
        <.input
          type="select"
          name="type"
          options={[{"Full Transcription", "full"}, {"Short (Account ID)", "truncated"}]}
          value={@session_type}
          class="mx-2 px-2"
        />
        <div class="m-auto pl-4 text-sm text-zinc-500 font-light">Next Form:</div>
        <.input
          type="select"
          name="next_form"
          prompt="All Forms"
          options={@next_form_options}
          value={@next_form}
          class="mx-2 px-2 w-48"
        />
        <div class="m-auto pl-4 text-sm text-zinc-500 font-light">Next Status:</div>
        <.input
          type="select"
          name="next_status"
          prompt="Search for..."
          options={@next_options}
          value={@next_status}
          class="mx-2 px-2"
        />
      </.form>
      <div
        phx-click="next"
        class="bg-gray-500 m-auto p-1 mx-1 text-white font-sans font-semibold rounded-xs cursor-pointer"
      >
        Next
      </div>
    </div>
  </div>

  <!-- Conflict Resolution Banner -->
  <.conflict_resolution_banner
    claim_conflict={assigns[:claim_conflict] || false}
    previous_claimer={assigns[:previous_claimer] || false}
    read_only={assigns[:read_only] || false}
    same_user_conflict={assigns[:same_user_conflict] || false} />

  <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
    <div :if={is_nil(@current)} class="flex h-24 items-center justify-center">
      <p class="text-center text-xl font-light text-gray-500">
        No mailer loaded, when ready, hit next.
      </p>
    </div>
    <div :if={!is_nil(@current) and !is_nil(@form)} class="flex py-4 px-8">
      <div class="columns-1 px-4 py-3 pt-5">
        <div class="mt-3">
          <div class="mb-3 text-xl font-semibold text-gray-700">Mailer Core Information</div>
          <div class="mx-6">
            <h3>
              <b>Mailer</b> <%= @current.original_name %>
              <span
                :if={@current.exception}
                class="inline-flex items-center rounded-xs bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800"
              >
                <svg class="mr-1.5 h-2 w-2 text-amber-400" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Known Exception
              </span>
            </h3>

            <h3><b>Mailer ID:</b> <code id="mailer-id"><%= @current.id %></code>
              <.copy_button id="mailer-id-copy" content="mailer-id" />
            </h3>
            <h3><b>Current Status</b> <%= @current.status %></h3>
            <h3>
              <b>Scan Link:</b>
              <.external_link text="Open Scan" href={@current.image_url} target="crud_viewer" />
            </h3>
            <div>
              <b>Form:</b>
              <%= @form.name %>
              <b>Source:</b>
              <div class="inline-block">
                <.form
                  :let={f}
                  id={"#{@current.id}-assignment-form"}
                  for={@form_form}
                  phx-submit="change_form"
                  class="flex"
                >
                  <.input type="hidden" field={f[:mailer_id]} value={@current.id} />
                  <.input
                    type="select"
                    name="form_id"
                    options={@known_forms}
                    value={@form.id}
                    class="mx-2 px-2"
                  />
                  <button
                    type="submit"
                    class="block text-sm font-sans font-semibold bg-slate-500 text-white ml-3 mt-1 px-2 py-0.5 ph-2 rounded-xs text-center leading-tight"
                  >
                    Change Form
                  </button>
                </.form>
              </div>
            </div>
            <h3 :if={@current.account_number}>
              <b>Account Number:</b>

              <span :if={@current.account_number}><%= @current.account_number %></span>
              <span :if={!@current.account_number} class="text-zinc-500 font-light">
                Not transcribed
              </span>
              <span
                :if={@current.not_interested}
                class="inline-flex items-center rounded-xs bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800"
              >
                <svg class="mr-1.5 h-2 w-2 text-red-400" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Not Interested
              </span>

              <span
                :if={@current.account_number_validated}
                class="inline-flex items-center rounded-xs bg-lime-100 px-2 py-0.5 text-xs font-medium text-green-800"
              >
                <svg class="mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Validated
              </span>
              <span
                :if={!@current.account_number_validated}
                class="inline-flex items-center rounded-xs bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800"
              >
                <svg class="mr-1.5 h-2 w-2 text-amber-400" fill="currentColor" viewBox="0 0 8 8">
                  <circle cx="4" cy="4" r="3" />
                </svg>
                Pending Validation
              </span>
            </h3>
            <h3 :if={!@current.account_number}>
              <b>Account Number:</b> <span class="text-zinc-500 font-light">Not transcribed</span>
            </h3>
          </div>
        </div>
        <div class="mt-3">
          <div class="mb-3 text-xl font-semibold text-gray-700">Controls and Fields</div>
          <div class="mx-6 columns-1">
            <div :if={@session_type == "truncated"} class="columns-1">
              <div class="flex flex-row mb-4">
                <div class="flex-1 text-right font-semibold text-zinc-600 columns-1 p-2">
                  <div>Account Number</div>
                  <div class="text-xs">(numbers only)</div>
                  <div class="text-xs">For Drovers (Red Forms), exclude the last digit</div>
                </div>
                <div class="flex-1 p-2">
                  <.form
                    :let={f}
                    as={:account_bits}
                    for={@account_form}
                    phx-submit="verify_account_number"
                    class="w-full"
                  >
                    <.input type="hidden" field={f[:source]} />
                    <.input field={f[:account]} class="w-full" value={@current.account_number} />

                    <button
                      type="submit"
                      disabled={assigns[:read_only] || false}
                      class={[
                        "block text-sm font-sans font-semibold mt-1 py-1 w-full rounded-xs text-center leading-tight",
                        if(assigns[:read_only], do: "bg-gray-400 text-gray-600 cursor-not-allowed", else: "bg-slate-500 text-white")
                      ]}
                    >
                      <%= if assigns[:read_only], do: "Read Only", else: "Verify" %>
                    </button>
                  </.form>
                </div>
                <div class="flex-1 p-2">
                  <div :if={@lookup_done && @customer}>
                    <div class="text-zinc-600">Found Customer Data:</div>
                    <pre class="font-sans font-semibold p-3 m-1 rounded-md border-solid border-1 border-zinc-400">
                                        <%= @customer.id %>
                                        <%= @customer.first_name %> <%= @customer.last_name %>
                                        <%= @customer.address_1 %>
                                        <%= @customer.address_2 %>
                                        <%= @customer.city %> <%= @customer.state %> <%= @customer.zip %>
                                        </pre>
                    <div class="text-zinc-800 font-light">
                      Account Information saved! Please move on to next mailer
                    </div>
                  </div>
                  <div :if={@lookup_done && !@customer}>
                    <div class="text-zinc-600">Account Information Not Found:</div>
                    <pre class="font-sans font-semibold p-3 m-1 rounded-md border-solid border-1 border-zinc-400">
                                        Account Number: <%= @current.account_number %>
                                        Source: <%= @current.source %>
                                        </pre>
                    <div class="text-zinc-800 font-light">
                      Please check this mailers assigned source, or correct the account number.
                    </div>
                  </div>
                  <div :if={!@lookup_done}>
                    Press Verify
                  </div>
                </div>
              </div>
              <div class="columns-1">
                <.form :let={f} for={@notes_form} phx-submit="save_client_notes" class="w-full">
                  <div class="text-lg font-semibold text-gray-700">
                    Notes (SUBMITTED TO CLIENT)
                  </div>
                  <.input
                    type="textarea"
                    field={f[:client_notes]}
                    class="w-full"
                    value={@current.client_notes}
                  />
                  <button
                    type="submit"
                    disabled={(@saved || assigns[:read_only]) || false}
                    class={[
                      (@saved || assigns[:read_only]) && "disabled cursor-not-allowed",
                      if(assigns[:read_only], do: "bg-gray-400 text-gray-600", else: "bg-slate-500 text-white"),
                      "block text-sm font-sans font-semibold mt-1 py-1 w-full rounded-xs text-center leading-tight"
                    ]}
                  >
                    <%= if assigns[:read_only], do: "Read Only", else: "Save Notes" %>
                  </button>
                  <small :if={@saved}>Notes saved! Please continue</small>
                </.form>
              </div>
              <div class="flex py-3 justify-between">
                <div
                  phx-click="mark_exception"
                  class="bg-orange-200 text-black cursor-pointer px-3 py-1 rounded-md"
                >
                  Mark as Exception
                </div>
                <div
                  :if={!@current.account_number_validated && @current.not_interested == false}
                  disabled
                  title="Verify Account Number First"
                  class="bg-red-200 text-black cursor-not-allowed px-3 py-1 rounded-md"
                >
                  Mark as Not Interested
                </div>

                <div
                  :if={@current.account_number_validated && @current.not_interested == false}
                  phx-click="mark_not_interested"
                  class="bg-red-200 text-black cursor-pointer px-3 py-1 rounded-md"
                >
                  Mark as Not Interested
                </div>
                <div
                  :if={@current.not_interested == true}
                  phx-click="mark_interested"
                  class="bg-green-200 text-black cursor-pointer px-3 py-1 rounded-md"
                >
                  Mark as Interested
                </div>
                <div
                  phx-click="next"
                  class="bg-slate-500 text-white px-3 py-1 cursor-pointer rounded-md"
                >
                  Next Mailer
                </div>
              </div>
            </div>

            <div :if={@session_type == "full"}>
              <.form :let={f} for={@full_form} phx-submit="save_and_next" class="w-full">
                <.input type="hidden" field={f[:id]} />
                <.account_input :if={!@current.account_number} for={f} />
                <.signed_date_input for={f} />
                <.form_input :for={field <- @form.fields} for={f} field={field} />

                <div class="flex py-3 justify-end">
                  <.button phx-disable-with="Saving...">Save and next</.button>
                </div>
              </.form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div :if={!is_nil(@current) and is_nil(@form)} class="flex py-4 px-8">
    <div class="columns-1 px-4 py-3 pt-5">
      <h3>Mailer <%= @current.original_name %></h3>
      <p>
        Scan Link:
        <.external_link text="Open Scan" href={@current.image_url} target="crud_viewer" />
      </p>
      <h3>Form: UNKNOWN, Source: Untranscribed</h3>

      <div :for={{source, id} <- @known_forms} class="px-4 py-6">
        <div>
          Source: <%= source %>
          <.button phx-click="change_form" phx-value-id={id}>Assign & Transcribe</.button>
        </div>
      </div>
    </div>
  </div>
</div>
