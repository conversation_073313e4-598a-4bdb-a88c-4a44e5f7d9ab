defmodule AdminWeb.Transcription.TranscribeLive.Index do
  use AdminWeb, :live_view

  alias Phoenix.PubSub

  alias Admin.Transcription
  alias Admin.Transcription.{Mailer, Form, Response, ExistingContact}
  alias Admin.Accounts.User
  import AdminWeb.Transcription.TranscribeLive.Components
  import Ecto.Query, warn: false

  @impl true
  def mount(_params, _session, socket) do
    PubSub.subscribe(Admin.PubSub, "mailers:reporting")
    {:ok, stream(socket, :mailers, [])}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  # Bind the different options to functions that have docs, let those
  # functions match the full params.
  def apply_action(socket, :lookup, params), do: apply_lookup_action(socket, params)
  def apply_action(socket, :lookup_fields, params), do: apply_lookup_fields_action(socket, params)
  def apply_action(socket, :index, params), do: apply_index_action(socket, params)

  @doc """
  Called when the live view is being loaded with a specific mailer in the url params.
  """
  def apply_lookup_action(socket, %{"id" => id}) do
    case Mailer.get_by_id(id) do
      {:ok, mailer} ->
        socket
        |> _reset()
        |> view_mailer(mailer)

      {:error, _} ->
        socket
        |> _reset()
        |> put_flash(:info, "No mailer found with id #{id}")
        |> redirect(to: ~p"/transcription/transcribe")
    end
  end

  @doc """
  Called when the live view is being loaded with a specific mailer in the url params
  and we are specifically on the fields url.
  """
  def apply_lookup_fields_action(socket, %{"id" => id}) do
    case Mailer.get_by_id(id) do
      {:ok, mailer} ->
        socket
        |> _reset()
        |> view_mailer(mailer)
        |> assign(session_type: "full")
        |> _reset_forms()

      {:error, _} ->
        socket
        |> _reset()
        |> put_flash(:info, "No mailer found with id #{id}")
        |> redirect(to: ~p"/transcription/transcribe")
    end
  end

  @doc """
  Called when the live view hits the transcription page as a new transcription session.
  """
  def apply_index_action(socket, _params) do
    socket
    |> _reset()
    |> view_mailer(nil)
  end

  @doc """
  Called by another process, asking who is working on what mailer.
  """
  def handle_info({:who, reply}, %{assigns: %{current: current, loaded_at: loaded_at}} = socket) do
    diff = System.monotonic_time(:millisecond) - loaded_at
    send(reply, reply_who(current, socket.assigns.current_user, diff, socket.assigns.session_type))
    {:noreply, socket}
  end

  @impl true
  @doc """
  Part of the active de-claiming system. The declaimer asks if we are actively working on a mailer
  with a specific id. If we are, we respond with true, if not, we respond with false.
  """
  def handle_info({:active?, id, reply}, %{assigns: %{current: %{id: id}}} = socket) do
    send(reply, {id, true})
    {:noreply, socket}
  end

  @impl true
  def handle_info({:active?, id, reply}, socket) do
    send(reply, {id, false})
    {:noreply, socket}
  end



  @impl true
  def handle_info({:mailer_claimed, mailer_id, claimer_info}, socket) do
    # Someone else claimed a mailer we're viewing
    if socket.assigns.current && socket.assigns.current.id == mailer_id && !socket.assigns.read_only do
      socket =
        socket
        |> assign(read_only: true, claim_conflict: true)
        |> put_flash(:warning, "This mailer was just claimed by #{claimer_info.name}. You are now in read-only mode.")

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:viewing_mailer, mailer_id, viewer_info}, socket) do
    # Someone else started viewing a mailer we're viewing
    if socket.assigns.current && socket.assigns.current.id == mailer_id && !socket.assigns.read_only do
      socket =
        socket
        |> assign(read_only: true, claim_conflict: true)
        |> put_flash(:warning, "#{viewer_info.name} is now also viewing this mailer. You are now in read-only mode.")

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:check_viewing, mailer_id, requester_id, reply_to}, socket) do
    # Someone is asking who is viewing this mailer
    if socket.assigns.current && socket.assigns.current.id == mailer_id &&
       socket.assigns.current_user.id != requester_id do

      viewer_info = %{
        id: socket.assigns.current_user.id,
        name: socket.assigns.current_user.name || socket.assigns.current_user.email,
        session_type: socket.assigns.session_type
      }

      send(reply_to, {:viewing_response, mailer_id, viewer_info})
    end

    {:noreply, socket}
  end

  @impl true
  def handle_info({:stop_viewing_mailer, mailer_id, viewer_info}, socket) do
    # Someone stopped viewing a mailer - if we're in read-only mode because of them,
    # we could potentially switch back to edit mode
    if socket.assigns.current && socket.assigns.current.id == mailer_id &&
       socket.assigns.read_only && socket.assigns.claim_conflict do

      # Check if anyone else is still viewing
      case check_if_mailer_being_viewed(mailer_id, socket.assigns.current_user.id) do
        :no_conflict ->
          # No one else viewing, try to claim the mailer
          case socket.assigns.current |> Mailer.claim(actor: socket.assigns.current_user) do
            {:ok, claimed_mailer} ->
              socket =
                socket
                |> assign(current: claimed_mailer, read_only: false, claim_conflict: false)
                |> put_flash(:info, "#{viewer_info.name} stopped viewing this mailer. You can now edit it.")

              {:noreply, socket}

            {:error, _} ->
              # Still can't claim, stay in read-only
              {:noreply, socket}
          end

        {:conflict, _} ->
          # Someone else still viewing, stay in read-only
          {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  # Bindings are defined here, and the documentation is in each handle function itself.
  @impl true
  def handle_event("verify_account_number", params, socket),
    do: handle_verify_account_number(params, socket)

  @impl true
  def handle_event("save_client_notes", params, socket),
    do: handle_save_client_notes(params, socket)

  @impl true
  def handle_event("change_session", params, socket), do: handle_change_session(params, socket)
  @impl true
  def handle_event("change_form", params, socket), do: handle_change_form(params, socket)
  @impl true
  def handle_event("save_and_next", params, socket), do: handle_save_and_next(params, socket)
  @impl true
  def handle_event("next", params, socket), do: handle_next(params, socket)
  @impl true
  def handle_event("mark_exception", params, socket), do: handle_mark_exception(params, socket)
  @impl true
  def handle_event("mark_interested", params, socket), do: handle_mark_interested(params, socket)
  @impl true
  def handle_event("mark_not_interested", params, socket),
    do: handle_mark_not_interested(params, socket)
  @impl true
  def handle_event("resolve_conflict", params, socket), do: handle_resolve_conflict(params, socket)
  @impl true
  def handle_event("force_reclaim", params, socket), do: handle_force_reclaim(params, socket)

  @doc """
  Called when a user clicks the verify account number button.
  """
  def handle_verify_account_number(
        %{"account_bits" => %{"source" => source, "account" => account}},
        socket
      ) do
    case ExistingContact.lookup(source, account) do
      {:ok, customer} ->
        current =
          socket.assigns.current
          |> Mailer.update_account!(to_string(customer.id), actor: socket.assigns.current_user)

        socket =
          socket
          |> assign(lookup_done: true, customer: customer, current: current)

        {:noreply, socket}

      _ ->
        socket =
          socket
          |> put_flash(:warning, "No contact found, please check for padded account number.")
          |> assign(lookup: true, customer: nil)

        {:noreply, socket}
    end
  end

  @doc """
  Called when the user clicks the save button on the client notes field.
  """
  def handle_save_client_notes(%{"client_notes" => client_notes}, socket) do
    if socket.assigns.read_only do
      {:noreply,
       socket
       |> put_flash(:error, "Cannot save changes in read-only mode. This mailer is being worked on by another user.")}
    else
      mailer =
        socket.assigns.current
        |> Mailer.update_client_notes!(client_notes, actor: socket.assigns.current_user)

      {:noreply, socket |> assign(current: mailer, saved: true)}
    end
  end

  @doc """
  Called when the user changes the "session view" from the dropdown menu.
  """
  def handle_change_session(
        %{"next_form" => form, "type" => type, "next_status" => next_status},
        socket
      ) do
    socket =
      socket
      |> assign(session_type: type, next_status: next_status, next_form: form)
      |> _reset_forms()
      |> _maybe_patch(type)

    {:noreply, socket}
  end

  def _maybe_patch(%{assigns: %{current: current}} = socket, _type) when is_nil(current),
    do: socket

  def _maybe_patch(socket, "full") do
    socket
    |> push_patch(to: ~p"/transcription/transcribe/#{socket.assigns.current}/fields")
  end

  def _maybe_patch(socket, "truncated") do
    socket
    |> push_patch(to: ~p"/transcription/transcribe/#{socket.assigns.current}")
  end

  def _maybe_patch(socket, _), do: socket

  @doc """
  Called when the user clicks the "assign form" button after selecint a new valid form from the
  dropdown menu.
  """
  def handle_change_form(%{"mailer_id" => _id, "form_id" => form_id}, socket) do
    form = Ash.get!(Form, form_id)

    socket =
      socket.assigns.current
      |> Ash.Changeset.for_update(:assign, %{form_id: form_id, source: form.source})
      |> Ash.update()
      |> then(fn
        {:ok, mailer} ->
          view_mailer(socket, mailer)

        {:error, e} ->
          socket
          |> put_flash(
            :info,
            "There was an error changing the mailers assigned form: #{inspect(e)}"
          )
      end)

    {:noreply, socket}
  end

  @doc """
  Called when the user is done with a mailer and clicks the save and next button from
  the full form, specifically the bottom of the transcription page.
  """
  def handle_save_and_next(params, socket) do
    if socket.assigns.read_only do
      {:noreply,
       socket
       |> put_flash(:error, "Cannot save changes in read-only mode. This mailer is being worked on by another user.")}
    else
      :ok = params |> save_responses(socket.assigns.current_user)
      next(socket)
    end
  end

  @doc """
  Called when the user clicks the next button at the top of the UI.
  """
  def handle_next(_params, socket) do
    next(socket)
  end

  @doc """
  Called when the user hits the "mark exception" button on the UI.
  """
  def handle_mark_exception(_params, socket) do
    socket.assigns.current
    |> Mailer.mark_exception!(actor: socket.assigns.current_user)

    socket
    |> next()
  end

  @doc """
  Called when the user hits the "mark rejected" button on the UI.
  """
  def handle_mark_not_interested(_params, socket) do
    socket.assigns.current
    |> Mailer.mark_not_interested!(actor: socket.assigns.current_user)

    socket
    |> next(update: false)
  end

  @doc """
  Called when the user hits the "mark interested" button on the UI.
  """
  def handle_mark_interested(_params, socket) do
    current =
      socket.assigns.current
      |> Mailer.mark_interested!(actor: socket.assigns.current_user)

    socket =
      socket
      |> assign(current: current)

    {:noreply, socket}
  end

  @doc """
  Called when user chooses to resolve a claim conflict by getting the next available mailer.
  """
  def handle_resolve_conflict(_params, socket) do
    socket
    |> put_flash(:info, "Getting next available mailer...")
    |> next()
  end

  @doc """
  Called when user chooses to force reclaim a mailer they previously had.
  This should be used carefully and might require additional permissions.
  """
  def handle_force_reclaim(_params, socket) do
    if socket.assigns.previous_claimer do
      # First declaim the mailer, then try to claim it
      case socket.assigns.current |> Mailer.declaim() do
        {:ok, declaimed_mailer} ->
          case declaimed_mailer |> Mailer.claim(actor: socket.assigns.current_user) do
            {:ok, reclaimed_mailer} ->
              socket =
                socket
                |> assign(current: reclaimed_mailer, read_only: false, claim_conflict: false)
                |> put_flash(:info, "Successfully reclaimed mailer.")
                |> _reset_forms()

              {:noreply, socket}

            {:error, _} ->
              socket =
                socket
                |> put_flash(:error, "Could not reclaim mailer. It may be actively being worked on.")

              {:noreply, socket}
          end

        {:error, _} ->
          socket =
            socket
            |> put_flash(:error, "Could not release mailer for reclaiming.")

          {:noreply, socket}
      end
    else
      socket =
        socket
        |> put_flash(:error, "You cannot force reclaim a mailer you didn't previously have.")

      {:noreply, socket}
    end
  end

  def _reset(socket) do
    assigns = socket.assigns

    socket
    |> _unsubscribe()
    |> assign(
      form_sorce_selection: "",
      known_forms: list_known_forms(),
      page_title: "Transcription",
      next_status: Map.get(assigns, :next_status, "new"),
      next_options: next_options(),
      next_form: Map.get(assigns, :next_form, ""),
      next_form_options: list_form_options(),
      session_type: Map.get(assigns, :session_type, "truncated"),
      customer: nil,
      current: nil,
      lookup_done: false,
      saved: false
    )
    |> _reset_forms()
  end

  @doc """
  Resets all forms used by the UI consistently.

  If the user is in the truncated session type, then the full form is reset to nil, and the
  truncated form is reset to a new form.

  No forms use eachothers data.
  """
  def _reset_forms(%{assigns: %{session_type: "truncated"}} = socket) do
    socket
    |> assign(
      session_form: session_form(),
      full_form: nil,
      form_form: form_form(socket),
      account_form: account_form(socket),
      notes_form: notes_form(socket),
      loaded_at: System.monotonic_time(:millisecond)
    )
  end

  def _reset_forms(%{assigns: %{session_type: "full"}} = socket) do
    socket
    |> assign(
      session_form: session_form(),
      full_form: full_form(socket),
      form_form: nil,
      account_form: nil,
      notes_form: nil,
      loaded_at: System.monotonic_time(:millisecond)
    )
  end

  def session_form() do
    %{
      "type" => "truncated"
    }
    |> to_form()
  end

  def form_form(%{assigns: %{current: nil}}), do: nil

  def form_form(socket) do
    current = socket.assigns.current

    %{
      "id" => current.id,
      "form_id" => current.form_id
    }
    |> to_form(id: "#{current.id}-assignment-form")
    |> Map.put(:hidden, ["id"])
  end

  def account_form(%{assigns: %{current: nil}}), do: nil

  def account_form(socket) do
    current = socket.assigns.current

    %{
      "account" => current.account_number,
      "source" => current.source
    }
    |> to_form(id: "#{current.id}-account-form")
    |> Map.put(:hidden, ["source"])
  end

  def notes_form(%{assigns: %{current: nil}}), do: nil

  def notes_form(socket) do
    current = socket.assigns.current

    %{
      "client_notes" => current.client_notes
    }
    |> to_form(id: "#{current.id}-notes-form")
  end

  def full_form(%{assigns: %{current: nil}}), do: nil

  def full_form(socket) do
    current = socket.assigns.current

    socket.assigns.form
    |> to_form(id: current.id, mailer: current)
  end

  @doc """
  Parses through the responses of the full form.

  In the form are the main fields such as mailer ID, but
  there are also one field per response. This function parses
  through the responses and saves them to the database.
  """
  def save_responses(%{"id" => id} = responses, actor) do
    response_ids = responses |> parse_response_ids()

    for response_id <- response_ids do
      Response.create_or_update!(id, response_id, responses[response_id], actor: actor)
    end

    responses
    |> maybe_update_fields(actor)

    :ok
  end

  @doc """
  Only try to parse actual responses, not the main fields.
  """
  def parse_response_ids(responses) do
    responses
    |> Map.keys()
    |> Enum.reject(&(&1 in ["id", "account", "form_id", "source", "signed_date"]))
  end

  @doc """
  Intercept the change where we now have a signed date.

  The date signiture is a first-class KPI that also gets its own field in the database.

  We make sure to update the mailer with the signed date.
  """
  def maybe_update_fields(%{"id" => id, "signed_date" => date} = responses, actor)
      when not is_nil(date) do
    Mailer
    |> Ash.get!(id)
    |> Mailer.update_signed_date!(date, actor: actor)

    less_responses = Map.drop(responses, ["signed_date"])
    maybe_update_fields(less_responses, actor)
  end

  def maybe_update_fields(%{"id" => id, "account" => account_number} = responses, actor) do
    Mailer
    |> Ash.get!(id)
    |> Mailer.update_account!(account_number, actor: actor)
  end

  def maybe_update_fields(_responses, _actor), do: nil

  @doc """
  Take a socket with any state, handle what is left, and move on to the next mailer.

  If the user completed everything, then we mark the mailer as completed, otherwise we mark it as
  previewed.
  """
  def next(%{assigns: %{next_status: status, next_form: form_id}} = socket, opts \\ []) do
    socket
    |> _mark_previewed_or_completed(opts)
    |> _reset_forms()

    # Use database-level locking to ensure atomic mailer assignment
    case get_next_mailer_atomic(status, form_id, socket.assigns.current_user) do
      {:ok, mailer} ->
        {:noreply,
         socket
         |> push_patch(to: ~p"/transcription/transcribe/#{mailer}")}

      {:error, :no_available_mailers} ->
        {:noreply,
         socket
         |> put_flash(:info, "No more mailers found with status #{socket.assigns.next_status}")}

      {:error, :all_mailers_claimed} ->
        {:noreply,
         socket
         |> put_flash(:info, "All available mailers are currently being worked on. Please try again in a moment.")}
    end
  end

  @doc """
  Changes various aspects of the socket to reflect the new mailer we'd like to view.

  We claim it (or try to), subscribe to the pubsub topic, and then update the socket with the
  new mailer and forms

  If we cleared the active mailer, then we simply unsubscribe from the pubsub topic.
  """
  def view_mailer(socket, nil) do
    # Announce that we're no longer viewing the current mailer
    if socket.assigns.current do
      announce_stop_viewing_mailer(socket.assigns.current.id, socket.assigns.current_user)
    end

    socket
    |> _unsubscribe()
    |> assign(current: nil)
    |> _reset_forms()
  end

  def view_mailer(socket, mailer) do
    # Check if someone else is currently viewing this mailer
    case check_if_mailer_being_viewed(mailer.id, socket.assigns.current_user.id) do
      {:conflict, viewer_info} ->
        # Someone else is viewing this mailer, put this user in read-only mode
        PubSub.subscribe(Admin.PubSub, "mailers:#{mailer.id}")

        socket
        |> put_flash(:warning, "This mailer is currently being viewed by #{viewer_info.name}. Opening in read-only mode.")
        |> assign(current: mailer, read_only: true, claim_conflict: true, previous_claimer: false)
        |> assign_mailer_form()
        |> _reset_forms()

      :no_conflict ->
        # If we were viewing a different mailer, announce we stopped viewing it
        if socket.assigns.current && socket.assigns.current.id != mailer.id do
          announce_stop_viewing_mailer(socket.assigns.current.id, socket.assigns.current_user)
        end

        # Announce that this user is now viewing this mailer
        announce_viewing_mailer(mailer.id, socket.assigns.current_user)

        # Check if this user previously had this mailer but it was declaimed
        was_previously_claimed = check_if_previously_claimed(socket.assigns.current_user.id, mailer.id)

        case mailer |> Mailer.claim(actor: socket.assigns.current_user) do
          {:ok, mailer} ->
            PubSub.subscribe(Admin.PubSub, "mailers:#{mailer.id}")

            socket
            |> assign(current: mailer, read_only: false, claim_conflict: false)
            |> assign_mailer_form()
            |> _reset_forms()

          {:error, _} ->
            # Check who currently has this mailer claimed
            current_claimer = get_current_claimer(mailer)

            conflict_message = if was_previously_claimed do
              "This mailer was automatically released due to inactivity and is now being worked on by #{current_claimer}. You can view it in read-only mode or get the next available mailer."
            else
              "Mailer #{mailer.id} is currently being worked on by #{current_claimer}. Opening in read-only mode."
            end

            PubSub.subscribe(Admin.PubSub, "mailers:#{mailer.id}")

            socket
            |> put_flash(:warning, conflict_message)
            |> assign(current: mailer, read_only: true, claim_conflict: true, previous_claimer: was_previously_claimed)
            |> assign_mailer_form()
            |> _reset_forms()
        end
    end
  end

  @doc """
  Not to be confused by the dumb name, this function takes the "form"
  as it is configured with responses for a given sourcecode set of mailers.

  The schema/field configuration of this form is loaded into the socket.
  """
  def assign_mailer_form(socket) do
    form =
      case Form.get(socket.assigns.current.form_id) do
        {:ok, form} -> form
        _ -> nil
      end

    socket
    |> assign(form: form)
  end

  @doc """
  Called when you wish to unsubscribe from the pubsub topic.

  This is a match function so we don't have to care if we actually have
  a current mailer or not.
  """
  def _unsubscribe(%{assigns: %{current: %{id: id}}} = socket) do
    PubSub.unsubscribe(Admin.PubSub, "mailers:#{id}")
    socket
  end

  def _unsubscribe(socket), do: socket

  @doc """
  Determintes if this finished mailer should be marked as simply previewed or completed.
  """
  def _mark_previewed_or_completed(socket, opts)
  def _mark_previewed_or_completed(socket, update: false), do: socket

  def _mark_previewed_or_completed(
        %{assigns: %{current: current, session_type: session_type}} = socket,
        _opts
      )
      when not is_nil(current) do
    actor = socket.assigns.current_user

    updated =
      case session_type do
        "truncated" ->
          socket.assigns.current
          |> Mailer.mark_previewed!(actor: actor)

        "full" ->
          socket.assigns.current
          |> Mailer.mark_completed!(actor: actor)
      end

    socket
    |> assign(current: updated)
  end

  def _mark_previewed_or_completed(socket, _), do: socket

  def reply_who(nil, user, started_at_diff, _session_type) do
    %{
      user: {user.id, user.name, user.email},
      session_type: nil,
      mailer: nil,
      for_seconds: started_at_diff / 1_000
    }
  end

  def reply_who(mailer, user, started_at_diff, session_type) do
    %{
      user: {user.id, user.name, user.email},
      session_type: session_type,
      mailer: {mailer.source, mailer.id},
      for_seconds: started_at_diff / 1_000
    }
  end

  @doc """
  Check if a user previously had this mailer claimed but it was declaimed.
  This helps detect when a user returns to a mailer that was auto-released.
  """
  defp check_if_previously_claimed(user_id, mailer_id) do
    # Check if this user was the previewed_by or transcribed_by for this mailer
    # but the mailer is no longer claimed
    case Mailer.get_by_id(mailer_id) do
      {:ok, mailer} ->
        (mailer.previewed_by_id == user_id or mailer.transcribed_by_id == user_id) and
        mailer.status != :claimed
      _ ->
        false
    end
  end

  @doc """
  Get information about who currently has a mailer claimed.
  """
  defp get_current_claimer(mailer) do
    case mailer.previewed_by_id do
      nil -> "another user"
      user_id ->
        case Ash.get(User, user_id) do
          {:ok, user} -> user.name || user.email
          _ -> "another user"
        end
    end
  end

  @doc """
  Check if someone else is currently viewing this mailer.
  Returns {:conflict, viewer_info} if someone else is viewing, :no_conflict otherwise.
  """
  defp check_if_mailer_being_viewed(mailer_id, current_user_id) do
    # Broadcast a check to see who is viewing this mailer
    PubSub.broadcast(Admin.PubSub, "mailers:#{mailer_id}", {:check_viewing, mailer_id, current_user_id, self()})

    # Wait for responses
    case collect_viewing_responses(mailer_id, 1000) do  # 1 second timeout
      [] -> :no_conflict
      [viewer_info | _] -> {:conflict, viewer_info}
    end
  end

  @doc """
  Announce that this user is now viewing a mailer.
  """
  defp announce_viewing_mailer(mailer_id, user) do
    viewer_info = %{
      id: user.id,
      name: user.name || user.email
    }

    PubSub.broadcast(Admin.PubSub, "mailers:#{mailer_id}", {:viewing_mailer, mailer_id, viewer_info})
  end

  @doc """
  Announce that this user is no longer viewing a mailer.
  """
  defp announce_stop_viewing_mailer(mailer_id, user) do
    viewer_info = %{
      id: user.id,
      name: user.name || user.email
    }

    PubSub.broadcast(Admin.PubSub, "mailers:#{mailer_id}", {:stop_viewing_mailer, mailer_id, viewer_info})
  end

  @doc """
  Collect responses about who is viewing a mailer.
  """
  defp collect_viewing_responses(mailer_id, timeout) do
    collect_viewing_responses(mailer_id, [], timeout, System.monotonic_time(:millisecond))
  end

  defp collect_viewing_responses(mailer_id, responses, remaining_timeout, start_time) do
    if remaining_timeout <= 0 do
      responses
    else
      receive do
        {:viewing_response, ^mailer_id, viewer_info} ->
          elapsed = System.monotonic_time(:millisecond) - start_time
          new_timeout = max(0, remaining_timeout - elapsed)
          collect_viewing_responses(mailer_id, [viewer_info | responses], new_timeout, start_time)

        _ ->
          # Ignore other messages
          elapsed = System.monotonic_time(:millisecond) - start_time
          new_timeout = max(0, remaining_timeout - elapsed)
          collect_viewing_responses(mailer_id, responses, new_timeout, start_time)
      after
        remaining_timeout ->
          responses
      end
    end
  end


  @doc """
  Returns the active forms that are configured for the transcription UI for selection.
  """
  def list_form_options do
    Ash.Query.for_read(Form, :read)
    |> Ash.read!()
    |> Enum.map(fn form -> {form.name, form.id} end)
  end

  @doc """
  Returns the active forms that are configured for the transcription UI.
  """
  def list_known_forms do
    Ash.Query.for_read(Form, :read)
    |> Ash.read!()
    |> Enum.map(fn form -> {form.source, form.id} end)
  end

  @doc """
  A simple helper to list the available "targets" to work on next, it's just a map to statuses.
  """
  def next_options,
    do: [
      {"NEW", "new"},
      {"2nd Pass", "previewed"},
      {"Exceptions", "exception"},
      {"Rejections", "rejected"},
      {"Focused Mailers", "focused"}
    ]

  @doc """
  Atomically get the next available mailer and claim it using database-level locking.
  This prevents race conditions when multiple users click "next" simultaneously.
  """
  defp get_next_mailer_atomic(status, form_id, user) do
    alias Admin.AdminRepo

    AdminRepo.transaction(fn ->
      # Find next available mailer with filters, excluding claimed ones
      case Mailer.next(status, form_id) do
        {:ok, mailer} ->
          # Try to claim the mailer
          case Mailer.claim(mailer, actor: user) do
            {:ok, claimed_mailer} ->
              claimed_mailer

            {:error, _} ->
              # Mailer was claimed by someone else, try to find another
              AdminRepo.rollback(:all_mailers_claimed)
          end

        {:error, _} ->
          AdminRepo.rollback(:no_available_mailers)
      end
    end)
    |> case do
      {:ok, mailer} -> {:ok, mailer}
      {:error, reason} -> {:error, reason}
    end
  end
end
