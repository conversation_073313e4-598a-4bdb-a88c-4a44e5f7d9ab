defmodule AdminWeb.Transcription.TranscribeLive.Index do
  use AdminWeb, :live_view

  alias Phoenix.PubSub

  alias Admin.Transcription
  alias Admin.Transcription.{Mailer, Form, Response, ExistingContact, MailerCoordinator}
  alias Admin.Accounts.User
  import AdminWeb.Transcription.TranscribeLive.Components
  import Ecto.Query, warn: false

  @impl true
  def mount(_params, _session, socket) do
    PubSub.subscribe(Admin.PubSub, "mailers:reporting")
    PubSub.subscribe(Admin.PubSub, "mailer_coordination")
    {:ok, stream(socket, :mailers, [])}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  # Bind the different options to functions that have docs, let those
  # functions match the full params.
  def apply_action(socket, :lookup, params), do: apply_lookup_action(socket, params)
  def apply_action(socket, :lookup_fields, params), do: apply_lookup_fields_action(socket, params)
  def apply_action(socket, :index, params), do: apply_index_action(socket, params)

  @doc """
  Called when the live view is being loaded with a specific mailer in the url params.
  """
  def apply_lookup_action(socket, %{"id" => id}) do
    case Mailer.get_by_id(id) do
      {:ok, mailer} ->
        socket
        |> _reset()
        |> view_mailer(mailer)

      {:error, _} ->
        socket
        |> _reset()
        |> put_flash(:info, "No mailer found with id #{id}")
        |> redirect(to: ~p"/transcription/transcribe")
    end
  end

  @doc """
  Called when the live view is being loaded with a specific mailer in the url params
  and we are specifically on the fields url.
  """
  def apply_lookup_fields_action(socket, %{"id" => id}) do
    case Mailer.get_by_id(id) do
      {:ok, mailer} ->
        socket
        |> _reset()
        |> view_mailer(mailer)
        |> assign(session_type: "full")
        |> _reset_forms()

      {:error, _} ->
        socket
        |> _reset()
        |> put_flash(:info, "No mailer found with id #{id}")
        |> redirect(to: ~p"/transcription/transcribe")
    end
  end

  @doc """
  Called when the live view hits the transcription page as a new transcription session.
  """
  def apply_index_action(socket, _params) do
    socket
    |> _reset()
    |> view_mailer(nil)
  end

  @doc """
  Called by another process, asking who is working on what mailer.
  """
  def handle_info({:who, reply}, %{assigns: %{current: current, loaded_at: loaded_at}} = socket) do
    diff = System.monotonic_time(:millisecond) - loaded_at
    send(reply, reply_who(current, socket.assigns.current_user, diff, socket.assigns.session_type))
    {:noreply, socket}
  end

  @impl true
  @doc """
  Part of the active de-claiming system. The declaimer asks if we are actively working on a mailer
  with a specific id. If we are, we respond with true, if not, we respond with false.
  """
  def handle_info({:active?, id, reply}, %{assigns: %{current: %{id: id}}} = socket) do
    send(reply, {id, true})
    {:noreply, socket}
  end

  @impl true
  def handle_info({:active?, id, reply}, socket) do
    send(reply, {id, false})
    {:noreply, socket}
  end

  @impl true
  def handle_info({:mailer_request, _, _, _, _, _} = msg, socket) do
    {:noreply, MailerCoordinator.handle_coordination_message(msg, socket)}
  end

  @impl true
  def handle_info({:mailer_assignment, _, _, _} = msg, socket) do
    {:noreply, MailerCoordinator.handle_coordination_message(msg, socket)}
  end

  # Bindings are defined here, and the documentation is in each handle function itself.
  @impl true
  def handle_event("verify_account_number", params, socket),
    do: handle_verify_account_number(params, socket)

  @impl true
  def handle_event("save_client_notes", params, socket),
    do: handle_save_client_notes(params, socket)

  @impl true
  def handle_event("change_session", params, socket), do: handle_change_session(params, socket)
  @impl true
  def handle_event("change_form", params, socket), do: handle_change_form(params, socket)
  @impl true
  def handle_event("save_and_next", params, socket), do: handle_save_and_next(params, socket)
  @impl true
  def handle_event("next", params, socket), do: handle_next(params, socket)
  @impl true
  def handle_event("mark_exception", params, socket), do: handle_mark_exception(params, socket)
  @impl true
  def handle_event("mark_interested", params, socket), do: handle_mark_interested(params, socket)
  @impl true
  def handle_event("mark_not_interested", params, socket),
    do: handle_mark_not_interested(params, socket)

  @doc """
  Called when a user clicks the verify account number button.
  """
  def handle_verify_account_number(
        %{"account_bits" => %{"source" => source, "account" => account}},
        socket
      ) do
    case ExistingContact.lookup(source, account) do
      {:ok, customer} ->
        current =
          socket.assigns.current
          |> Mailer.update_account!(to_string(customer.id), actor: socket.assigns.current_user)

        socket =
          socket
          |> assign(lookup_done: true, customer: customer, current: current)

        {:noreply, socket}

      _ ->
        socket =
          socket
          |> put_flash(:warning, "No contact found, please check for padded account number.")
          |> assign(lookup: true, customer: nil)

        {:noreply, socket}
    end
  end

  @doc """
  Called when the user clicks the save button on the client notes field.
  """
  def handle_save_client_notes(%{"client_notes" => client_notes}, socket) do
    mailer =
      socket.assigns.current
      |> Mailer.update_client_notes!(client_notes, actor: socket.assigns.current_user)

    {:noreply, socket |> assign(current: mailer, saved: true)}
  end

  @doc """
  Called when the user changes the "session view" from the dropdown menu.
  """
  def handle_change_session(
        %{"next_form" => form, "type" => type, "next_status" => next_status},
        socket
      ) do
    socket =
      socket
      |> assign(session_type: type, next_status: next_status, next_form: form)
      |> _reset_forms()
      |> _maybe_patch(type)

    {:noreply, socket}
  end

  def _maybe_patch(%{assigns: %{current: current}} = socket, _type) when is_nil(current),
    do: socket

  def _maybe_patch(socket, "full") do
    socket
    |> push_patch(to: ~p"/transcription/transcribe/#{socket.assigns.current}/fields")
  end

  def _maybe_patch(socket, "truncated") do
    socket
    |> push_patch(to: ~p"/transcription/transcribe/#{socket.assigns.current}")
  end

  def _maybe_patch(socket, _), do: socket

  @doc """
  Called when the user clicks the "assign form" button after selecint a new valid form from the
  dropdown menu.
  """
  def handle_change_form(%{"mailer_id" => _id, "form_id" => form_id}, socket) do
    form = Ash.get!(Form, form_id)

    socket =
      socket.assigns.current
      |> Ash.Changeset.for_update(:assign, %{form_id: form_id, source: form.source})
      |> Ash.update()
      |> then(fn
        {:ok, mailer} ->
          view_mailer(socket, mailer)

        {:error, e} ->
          socket
          |> put_flash(
            :info,
            "There was an error changing the mailers assigned form: #{inspect(e)}"
          )
      end)

    {:noreply, socket}
  end

  @doc """
  Called when the user is done with a mailer and clicks the save and next button from
  the full form, specifically the bottom of the transcription page.
  """
  def handle_save_and_next(params, socket) do
    :ok = params |> save_responses(socket.assigns.current_user)

    next(socket)
  end

  @doc """
  Called when the user clicks the next button at the top of the UI.
  """
  def handle_next(_params, socket) do
    next(socket)
  end

  @doc """
  Called when the user hits the "mark exception" button on the UI.
  """
  def handle_mark_exception(_params, socket) do
    socket.assigns.current
    |> Mailer.mark_exception!(actor: socket.assigns.current_user)

    socket
    |> next()
  end

  @doc """
  Called when the user hits the "mark rejected" button on the UI.
  """
  def handle_mark_not_interested(_params, socket) do
    socket.assigns.current
    |> Mailer.mark_not_interested!(actor: socket.assigns.current_user)

    socket
    |> next(update: false)
  end

  @doc """
  Called when the user hits the "mark interested" button on the UI.
  """
  def handle_mark_interested(_params, socket) do
    current =
      socket.assigns.current
      |> Mailer.mark_interested!(actor: socket.assigns.current_user)

    socket =
      socket
      |> assign(current: current)

    {:noreply, socket}
  end

  def _reset(socket) do
    assigns = socket.assigns

    socket
    |> _unsubscribe()
    |> assign(
      form_sorce_selection: "",
      known_forms: list_known_forms(),
      page_title: "Transcription",
      next_status: Map.get(assigns, :next_status, "new"),
      next_options: next_options(),
      next_form: Map.get(assigns, :next_form, ""),
      next_form_options: list_form_options(),
      session_type: Map.get(assigns, :session_type, "truncated"),
      customer: nil,
      current: nil,
      lookup_done: false,
      saved: false
    )
    |> _reset_forms()
  end

  @doc """
  Resets all forms used by the UI consistently.

  If the user is in the truncated session type, then the full form is reset to nil, and the
  truncated form is reset to a new form.

  No forms use eachothers data.
  """
  def _reset_forms(%{assigns: %{session_type: "truncated"}} = socket) do
    socket
    |> assign(
      session_form: session_form(),
      full_form: nil,
      form_form: form_form(socket),
      account_form: account_form(socket),
      notes_form: notes_form(socket),
      loaded_at: System.monotonic_time(:millisecond)
    )
  end

  def _reset_forms(%{assigns: %{session_type: "full"}} = socket) do
    socket
    |> assign(
      session_form: session_form(),
      full_form: full_form(socket),
      form_form: nil,
      account_form: nil,
      notes_form: nil,
      loaded_at: System.monotonic_time(:millisecond)
    )
  end

  def session_form() do
    %{
      "type" => "truncated"
    }
    |> to_form()
  end

  def form_form(%{assigns: %{current: nil}}), do: nil

  def form_form(socket) do
    current = socket.assigns.current

    %{
      "id" => current.id,
      "form_id" => current.form_id
    }
    |> to_form(id: "#{current.id}-assignment-form")
    |> Map.put(:hidden, ["id"])
  end

  def account_form(%{assigns: %{current: nil}}), do: nil

  def account_form(socket) do
    current = socket.assigns.current

    %{
      "account" => current.account_number,
      "source" => current.source
    }
    |> to_form(id: "#{current.id}-account-form")
    |> Map.put(:hidden, ["source"])
  end

  def notes_form(%{assigns: %{current: nil}}), do: nil

  def notes_form(socket) do
    current = socket.assigns.current

    %{
      "client_notes" => current.client_notes
    }
    |> to_form(id: "#{current.id}-notes-form")
  end

  def full_form(%{assigns: %{current: nil}}), do: nil

  def full_form(socket) do
    current = socket.assigns.current

    socket.assigns.form
    |> to_form(id: current.id, mailer: current)
  end

  @doc """
  Parses through the responses of the full form.

  In the form are the main fields such as mailer ID, but
  there are also one field per response. This function parses
  through the responses and saves them to the database.
  """
  def save_responses(%{"id" => id} = responses, actor) do
    response_ids = responses |> parse_response_ids()

    for response_id <- response_ids do
      Response.create_or_update!(id, response_id, responses[response_id], actor: actor)
    end

    responses
    |> maybe_update_fields(actor)

    :ok
  end

  @doc """
  Only try to parse actual responses, not the main fields.
  """
  def parse_response_ids(responses) do
    responses
    |> Map.keys()
    |> Enum.reject(&(&1 in ["id", "account", "form_id", "source", "signed_date"]))
  end

  @doc """
  Intercept the change where we now have a signed date.

  The date signiture is a first-class KPI that also gets its own field in the database.

  We make sure to update the mailer with the signed date.
  """
  def maybe_update_fields(%{"id" => id, "signed_date" => date} = responses, actor)
      when not is_nil(date) do
    Mailer
    |> Ash.get!(id)
    |> Mailer.update_signed_date!(date, actor: actor)

    less_responses = Map.drop(responses, ["signed_date"])
    maybe_update_fields(less_responses, actor)
  end

  def maybe_update_fields(%{"id" => id, "account" => account_number} = responses, actor) do
    Mailer
    |> Ash.get!(id)
    |> Mailer.update_account!(account_number, actor: actor)
  end

  def maybe_update_fields(_responses, _actor), do: nil

  @doc """
  Take a socket with any state, handle what is left, and move on to the next mailer.

  If the user completed everything, then we mark the mailer as completed, otherwise we mark it as
  previewed.
  """
  def next(%{assigns: %{next_status: status, next_form: form_id}} = socket, opts \\ []) do
    socket
    |> _mark_previewed_or_completed(opts)
    |> _reset_forms()

    # Release current mailer if any
    if socket.assigns.current do
      MailerAssignmentService.release_mailer(socket.assigns.current_user.id, socket.assigns.current.id)
    end

    # Use centralized assignment service
    case MailerAssignmentService.request_next_mailer(socket.assigns.current_user.id, status, form_id) do
      {:ok, mailer} ->
        {:noreply,
         socket
         |> push_patch(to: ~p"/transcription/transcribe/#{mailer}")}

      {:error, :no_available_mailers} ->
        {:noreply,
         socket
         |> put_flash(:info, "No more mailers found with status #{socket.assigns.next_status}")}

      {:error, :all_mailers_claimed} ->
        {:noreply,
         socket
         |> put_flash(:info, "All available mailers are currently being worked on. Please try again in a moment.")}
    end
  end

  @doc """
  Changes various aspects of the socket to reflect the new mailer we'd like to view.

  We claim it (or try to), subscribe to the pubsub topic, and then update the socket with the
  new mailer and forms

  If we cleared the active mailer, then we simply unsubscribe from the pubsub topic.
  """
  def view_mailer(socket, nil) do
    socket
    |> _unsubscribe()
    |> assign(current: nil)
    |> _reset_forms()
  end

  def view_mailer(socket, mailer) do
    case mailer |> Mailer.claim(actor: socket.assigns.current_user) do
      {:ok, mailer} ->
        PubSub.subscribe(Admin.PubSub, "mailers:#{mailer.id}")

        socket
        |> assign(current: mailer, read_only: false)
        |> assign_mailer_form()
        |> _reset_forms()

      {:error, _} ->
        # TODO: Alert a user that another user is looking at this mailer, ask if
        # they want to give up their claim and switch to read only mode.
        # TODO: Allow the user to switch to/from read only mode manually.
        socket
        |> put_flash(:info, "Mailer #{mailer.id} is already claimed. Opening in read only mode.")
        |> assign(current: mailer, read_only: true)
        |> assign_mailer_form()
        |> _reset_forms()
    end
  end

  @doc """
  Not to be confused by the dumb name, this function takes the "form"
  as it is configured with responses for a given sourcecode set of mailers.

  The schema/field configuration of this form is loaded into the socket.
  """
  def assign_mailer_form(socket) do
    form =
      case Form.get(socket.assigns.current.form_id) do
        {:ok, form} -> form
        _ -> nil
      end

    socket
    |> assign(form: form)
  end

  @doc """
  Called when you wish to unsubscribe from the pubsub topic.

  This is a match function so we don't have to care if we actually have
  a current mailer or not.
  """
  def _unsubscribe(%{assigns: %{current: %{id: id}}} = socket) do
    PubSub.unsubscribe(Admin.PubSub, "mailers:#{id}")
    socket
  end

  def _unsubscribe(socket), do: socket

  @doc """
  Determintes if this finished mailer should be marked as simply previewed or completed.
  """
  def _mark_previewed_or_completed(socket, opts)
  def _mark_previewed_or_completed(socket, update: false), do: socket

  def _mark_previewed_or_completed(
        %{assigns: %{current: current, session_type: session_type}} = socket,
        _opts
      )
      when not is_nil(current) do
    actor = socket.assigns.current_user

    updated =
      case session_type do
        "truncated" ->
          socket.assigns.current
          |> Mailer.mark_previewed!(actor: actor)

        "full" ->
          socket.assigns.current
          |> Mailer.mark_completed!(actor: actor)
      end

    socket
    |> assign(current: updated)
  end

  def _mark_previewed_or_completed(socket, _), do: socket

  def reply_who(nil, user, started_at_diff, _session_type) do
    %{
      user: {user.id, user.name, user.email},
      session_type: nil,
      mailer: nil,
      for_seconds: started_at_diff / 1_000
    }
  end

  def reply_who(mailer, user, started_at_diff, session_type) do
    %{
      user: {user.id, user.name, user.email},
      session_type: session_type,
      mailer: {mailer.source, mailer.id},
      for_seconds: started_at_diff / 1_000
    }
  end


  @doc """
  Returns the active forms that are configured for the transcription UI for selection.
  """
  def list_form_options do
    Ash.Query.for_read(Form, :read)
    |> Ash.read!()
    |> Enum.map(fn form -> {form.name, form.id} end)
  end

  @doc """
  Returns the active forms that are configured for the transcription UI.
  """
  def list_known_forms do
    Ash.Query.for_read(Form, :read)
    |> Ash.read!()
    |> Enum.map(fn form -> {form.source, form.id} end)
  end

  @doc """
  A simple helper to list the available "targets" to work on next, it's just a map to statuses.
  """
  def next_options,
    do: [
      {"NEW", "new"},
      {"2nd Pass", "previewed"},
      {"Exceptions", "exception"},
      {"Rejections", "rejected"},
      {"Focused Mailers", "focused"}
    ]
end
