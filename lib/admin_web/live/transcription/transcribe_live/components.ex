defmodule AdminWeb.Transcription.TranscribeLive.Components do
  @moduledoc """
  Provides UI functionality for the transcription live view.
  """
  use AdminWeb, :component
  alias Admin.Transcription.Field

  @doc """
  Renders the account number input.

  ## Examples

    <.account_input for={@form} />
  """
  def account_input(assigns) do
    ~H"""
    <div class="columns-2 mb-1">
      <div class="w-full text-zinc-600">
        Account Number (Numbers only)
      </div>
      <.input type="text" placeholder="Account Number (remove padding)" field={@for[:account]} />
    </div>
    """
  end

  @doc """
  Renders a date picker input.

  ## Examples

    <.date_input for={@form} />
  """
  def signed_date_input(assigns) do
    ~H"""
    <div class="columns-2 mb-1">
      <div class="w-full text-zinc-600">
        Date Signed (MM/DD/YYYY)
      </div>
      <.input type="date" field={@for[:signed_date]} />
    </div>
    """
  end

  def form_input(assigns) do
    ~H"""
    <hr :if={@field.new_block} class="py-3" />
    <div class="columns-2 mb-1">
      <div class={"w-full text-zinc-600 #{if @field.new_block, do: "font-semibold"}"}>
        #<%= @field.order_position %>, <%= @field.response_field_name %>
      </div>
      <.form_input_for
        field={@field}
        type={@field.type}
        name={input_name(@for, @field.id)}
        value={input_value(@for, @field.id)}
        label={nil}
      />
    </div>
    """
  end

  attr :type, :atom
  attr :field, Field
  attr :rest, :global, include: ~w(label name value)

  defp form_input_for(%{type: :text} = assigns) do
    ~H"""
    <.input type="text" {@rest} />
    """
  end

  defp form_input_for(%{type: :number} = assigns) do
    ~H"""
    <.input type="number" {@rest} />
    """
  end

  defp form_input_for(%{type: :checkbox} = assigns) do
    # Phoenix.HTML now prunes checkboxes with `value="false"`,
    # but at the same time now considers the `checked` field
    # to be used for the value (it used to ignore `checked`).
    # So, we have to do this.
    new_rest =
      assigns.rest
      |> Map.put(:value, "true")

    assigns =
      assigns
      |> assign(checked: Phoenix.HTML.Form.normalize_value("checkbox", assigns.rest.value))
      |> assign(rest: new_rest)

    ~H"""
    <.input type="checkbox"
      checked={@checked}
      {@rest} />
    """
  end

  defp form_input_for(%{type: :radio} = assigns) do
    ~H"""
    <div class="mx-3 inline-block">
      <ol>
        <li :for={opt <- @field.options || []}>
          <.input type="checkbox" {@rest}>
            <%= "#{opt["value"]} – #{opt["text"]}" %>
          </.input>
        </li>
      </ol>
    </div>
    """
  end

  defp form_input_for(%{type: :radio_text} = assigns) do
    ~H"""
    <div class="mx-3 inline-block flex-1">
      <.input type="text" {@rest} />
      <div class="text-xs font-sans text-zinc-500">Possable Responses</div>
      <div class="border-dashed border-2 rounded-lg border-zinc-500 p-2">
        <div :for={opt <- @field.options || []} class="text-sm text-">
          <span class="font-bold"><%= opt.value %></span>
          <span class="">-</span>
          <span class=""><%= opt.label %></span>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders conflict resolution options when a user encounters a claimed mailer.

  ## Examples

    <.conflict_resolution_banner
      claim_conflict={@claim_conflict}
      previous_claimer={@previous_claimer}
      read_only={@read_only} />
  """
  attr :claim_conflict, :boolean, default: false
  attr :previous_claimer, :boolean, default: false
  attr :read_only, :boolean, default: false
  attr :same_user_conflict, :boolean, default: false

  def conflict_resolution_banner(assigns) do
    ~H"""
    <div :if={@claim_conflict and @read_only} class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-yellow-800">
            Mailer Conflict Detected
          </h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p :if={@previous_claimer}>
              You previously had this mailer, but it was automatically released due to inactivity.
              Another user is now working on it.
            </p>
            <p :if={@same_user_conflict and not @previous_claimer}>
              This mailer is already open in another tab or browser session.
            </p>
            <p :if={not @previous_claimer and not @same_user_conflict}>
              This mailer is currently being viewed or worked on by another user.
            </p>
            <p class="mt-1">
              You are viewing this mailer in read-only mode. Choose an option below:
            </p>
          </div>
          <div class="mt-4 flex space-x-3">
            <button
              onclick="window.location.reload()"
              class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium">
              Refresh Page
            </button>
            <button
              phx-click="resolve_conflict"
              class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm font-medium">
              Get Next Available Mailer
            </button>
            <button
              :if={@previous_claimer and not @same_user_conflict}
              phx-click="force_reclaim"
              class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium">
              Force Reclaim (Use Carefully)
            </button>
            <button
              :if={@same_user_conflict}
              onclick="window.close()"
              class="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm font-medium">
              Close This Tab
            </button>
            <button
              onclick="window.history.back()"
              class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium">
              Go Back
            </button>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
