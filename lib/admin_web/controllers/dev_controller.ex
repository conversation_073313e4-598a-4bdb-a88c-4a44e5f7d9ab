defmodule AdminWeb.Controllers.DevController do
  use AdminWeb, :controller

  alias Admin.Accounts.User
  alias AdminWeb.UserAuth

  def init(default), do: default

  def user(conn, %{"role" => role}) do
    id = user_for(role)

    user =
      Ash.get!(User, id)

    conn
    |> put_session(:user_return_to, "/")
    |> alert_for(user)
    |> UserAuth.log_in_user(user)
  end

  def user_for(role)
  def user_for("admin"), do: 1
  def user_for("user"), do: 2
  def user_for("staff"), do: 2

  def alert_for(conn, %{role: :admin, name: name}) do

    conn
    |> put_flash(:warning, "Welcome Admin, #{name}!")
  end
  def alert_for(conn, %{role: :user, name: name}) do
    conn
    |> put_flash(:info, "Welcome, #{name}!")
  end

end
