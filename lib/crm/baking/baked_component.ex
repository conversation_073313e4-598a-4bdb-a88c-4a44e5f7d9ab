defmodule Crm.Baking.BakedComponent do
  @moduledoc """
  This module provides the schema and common functions for the BakedRecords.
  """
  require Logger

  import Ecto.Query, warn: false

  alias Crm.Baking.{Utilities, BakedRecordBySource, BakedResponse, BakedRecord}
  alias Crm.{Crc, Dial, History, Demo, Contact, Repo, EffortProjects, EffortProjectPricing}
  alias Crm.Project

  @type group_info :: %{
          project_id: integer,
          field_1: String.t(),
          date: Date.t(),
          total_time: integer,
          component_count: integer,
          # total_time / component_count rounded down.
          total_time_per_container: integer
        }

  @typedoc """
  This is the schema for the BakedRecordBySource.

  `group_info` may not exist, if you call `populate_group_info`, it will be added or set to false if it is not group calling.
  """
  @type t :: %__MODULE__{
          history_id: integer,
          crc: Crm.Crc,
          source_id: integer,
          rate: float,
          pricing_method: String.t(),
          max_attempts: integer,
          log: History.t(),
          dial: Dial.t(),
          responses: [Crm.Baking.BakedResponse.t()] | nil,
          raw_demos: [Crm.Baking.Demo.t()] | nil,
          contact: Contact.t(),
          group_info: group_info | false | nil
        }

  defstruct [
    :history_id,
    :crc,
    :source_id,
    :rate,
    :pricing_method,
    :max_attempts,
    :log,
    :dial,
    :responses,
    :raw_demos,
    :contact,
    :group_info
  ]

  @doc """
  This function is responsible for getting all the `BakedRecordLog` records for a given BakeID.
  """
  @spec get_baked_record_logs(bake_id :: integer) :: {:ok, [__MODULE__.t()]} | {:error, term}
  def get_baked_record_logs(bake_id) do
    query =
      _build_query()
      |> where([h], h.bake_id == ^bake_id)

    results =
      query
      |> Repo.all()
      |> Enum.map(&_normalize_crc/1)
      |> Enum.map(&_normalize_rate_and_method/1)
      |> Enum.map(&_normalize_source_id/1)
      |> _fetch_demos()

    {:ok, results}
  end

  def get_components_for_history_ids(history_ids) when is_list(history_ids) do
    query =
      _build_query()
      |> where([h], h.history_id in ^history_ids)

    results =
      query
      |> Repo.all()
      |> Enum.map(&_normalize_crc/1)
      |> Enum.map(&_normalize_rate_and_method/1)
      |> Enum.map(&_normalize_source_id/1)
      |> _fetch_demos()

    {:ok, results}
  end

  defp _build_query() do
    from(h in History,
      join: b in BakedRecord,
      on: h.bake_id == b.bake_id,
      join: d in Dial,
      on: d.dial_id == h.dial_id,
      join: c in Contact,
      on: c.dial_id == d.dial_id,
      left_join: crc in Crc,
      on: h.crc == crc.crc,
      # BUG/TODO: This should be a full join, but inbound baked data comes in as projectid = 0 🤔
      left_join: p in Project,
      on: b.project_id == p.id,
      left_join: ep in EffortProjects,
      on: p.id == ep.project_id,
      left_join: epp in EffortProjectPricing,
      on:
        epp.effort_project_id == ep.effort_project_id and
          (epp.source == "" or is_nil(epp.source) or
             epp.source == h.source_id) and
          (is_nil(epp.rate_start) or epp.rate_start <= b.call_date) and
          (is_nil(epp.rate_end) or epp.rate_end >= b.call_date),
      select: %__MODULE__{
        history_id: h.history_id,
        crc: crc,
        source_id: h.source_id,
        rate: epp.rate,
        pricing_method: epp.pricing_method,
        log: h,
        dial: d,
        max_attempts: p.max_attempts,
        contact: %Contact{
          contact_id: c.contact_id,
          dial_id: c.contact_id,
          # Only including fields we NEED here, because contact has 200+ columns of HUGE data lengths
          email: c.email
        }
      }
    )
  end

  defp _fetch_demos(components) do
    contact_ids =
      components
      # TODO: remove SA, and rebake when QC approves a record... Alternatively, we could just leave SA and only rebake on pull to lower metrics...
      # Comment came up that the responses don't match the report; this is because the report only pulls verified sales and this includes SAs
      # FIXME: Patch when qc event stream is looked at
      |> Enum.filter(fn c -> c.crc.crc in ~w(SA VS VSP VSW VSQP VSQ) end)
      |> Enum.map(fn c -> c.log.dial_id end)

    demos = Demo.fetch_metric_demos(contact_ids)

    components
    # TODO: Replace with |> _reduce_demos(demos)
    |> Enum.map(fn c ->
      demos =
        demos
        |> Enum.filter(fn d -> d.contact_id == c.log.dial_id end)

      %__MODULE__{c | responses: demos}
    end)
  end

  defp _normalize_crc(%__MODULE__{crc: nil} = log) do
    %__MODULE__{
      log
      | crc: %Crc{
          crc: "Unkwn",
          description: "Null CRC",
          contact: false,
          final_crc: false,
          success: false
        }
    }
  end

  defp _normalize_crc(%__MODULE__{crc: %Crc{}} = log), do: log
  defp _normalize_crc(_), do: raise("Invalid crc")

  defp _normalize_rate_and_method(%__MODULE__{rate: nil} = log) do
    %__MODULE__{
      log
      | rate: 0.0,
        pricing_method: "Unpriced"
    }
  end

  defp _normalize_rate_and_method(%__MODULE__{rate: rate} = log) when is_float(rate), do: log

  defp _normalize_rate_and_method(%__MODULE__{rate: rate} = log) when is_struct(rate, Decimal) do
    %__MODULE__{
      log
      | rate: Decimal.to_float(rate)
    }
  end

  defp _normalize_rate_and_method(_), do: raise("Invalid rate/method")

  defp _normalize_source_id(%__MODULE__{source_id: source} = log) when source in [nil, ""] do
    %__MODULE__{
      log
      | source_id: "Unsourced"
    }
  end

  defp _normalize_source_id(%__MODULE__{source_id: source_id} = log) when is_binary(source_id),
    do: log

  defp _normalize_source_id(_), do: raise("Invalid source_id")

  # START: Action reduction
  @doc """
  Reduce a list of __MODULE__s to a list of BakedRecordsBySource.

  ## Options
  - `:repair_time?` - If true, will repair the time on the components. This is an expensive operation, though it is batched.
  - `:force_repair_time?` - If true, will repair the time on the components. This is an expensive operation, though it is batched. This will force the repair, even if the component has already been repaired.
  """
  def reduce_to_baked_records_by_source(baked_components, opts \\ []) do
    repair? = Keyword.get(opts, :repair_time?, false)
    force_repair? = Keyword.get(opts, :force_repair_time?, false)

    baked_with_group =
      baked_components
      |> _append_group_info()

    baked_with_group =
      case repair? do
        true -> baked_with_group |> Utilities.repair_time(force?: force_repair?)
        false -> baked_with_group
      end

    brbs_list =
      baked_with_group
      |> Enum.group_by(&Map.get(&1, :source_id))
      |> Enum.map(fn {_source_id, [%__MODULE__{} | _] = components} ->
        components
        |> Enum.reduce(%BakedRecordBySource{}, &_reduce_source/2)
        |> _reduce_responses(components)
        |> _calculate_work_hours()
        |> _calculate_revenue()
      end)

    {:ok, brbs_list}
  end

  defp _append_group_info(components) do
    group_calling_components =
      components
      |> Enum.filter(&_is_group_calling/1)

    # Lookup helpers
    container_contact_ids =
      group_calling_components
      |> Enum.map(& &1.log.dial_id)

    call_dates =
      group_calling_components
      |> Enum.map(& &1.log.call_date)
      |> Enum.uniq()

    group_lookup_table =
      from(
        container in Dial,
        join: group in Dial,
        on:
          group.field_1 == container.field_1 and
            group.field_2 == container.project_id,
        join: h in History,
        on: h.dial_id == group.dial_id,
        where: container.dial_id in ^container_contact_ids and h.call_date in ^call_dates,
        select: {h.call_date, group.dial_id, group.field_1, container.project_id, h.total_time}
      )
      |> Crm.Repo.all()
      |> Enum.map(fn {call_date, dial_id, field_1, project_id, total_time} ->
        Logger.debug("Looping over record #{inspect(dial_id)} with time #{total_time}")

        components_that_apply =
          group_calling_components
          |> Enum.count(fn component ->
            component.log.call_date == call_date and
              component.dial.field_1 == field_1 and
              component.dial.project_id == project_id
          end)

        IO.puts(
          "SPLITTING GROUP TIME: Total Time; #{inspect(total_time)} cta; #{inspect(components_that_apply)}"
        )

        per_division = floor(total_time / components_that_apply)

        %{
          call_date: call_date,
          field_1: field_1,
          project_id: project_id,
          total_time: total_time,
          components_that_apply: components_that_apply,
          total_time_per_container: per_division
        }
      end)

    components
    |> Enum.map(fn component ->
      case group_lookup_table |> _find_group_info(component) do
        nil -> %__MODULE__{component | group_info: false}
        group_info -> %__MODULE__{component | group_info: group_info}
      end
    end)
  end

  defp _find_group_info(group_lookup_table, %__MODULE__{log: %History{} = h, dial: %Dial{} = dial}) do
    group_lookup_table
    |> Enum.find(fn %{call_date: call_date, field_1: field_1, project_id: project_id} ->
      h.call_date == call_date and
        dial.field_1 == field_1 and
        dial.project_id == project_id
    end)
  end

  defp _is_group_calling(%__MODULE__{dial: dial}), do: _is_group_calling(dial)

  defp _is_group_calling(%Dial{field_1: field_1, field_2: field_2})
       when not is_nil(field_1) or not is_nil(field_2),
       do: true

  defp _is_group_calling(_), do: false

  @spec _reduce_source(bc :: t, brbs :: BakedRecordBySource.t()) :: BakedRecordBySource.t()
  defp _reduce_source(%__MODULE__{source_id: nil} = bc, brbs) do
    _reduce_source(%__MODULE__{bc | source_id: "Unsourced"}, brbs)
  end

  defp _reduce_source(%__MODULE__{} = bc, %BakedRecordBySource{source_id: nil} = brbs) do
    new_brbs =
      %BakedRecordBySource{
        brbs
        | bake_id: bc.log.bake_id,
          source_id: bc.source_id,
          rate: bc.rate,
          pricing_method: bc.pricing_method
      }

    _reduce_source(bc, new_brbs)
  end

  @verified_sales ~W(VS VSP VSW VSQ VSQP)
  @unverified_sales ~W(SA)
  @vsqps ~W(VSQP)
  @warnings ~W(VSW)
  @unchecked_sales ~W(VSP)
  @pulled_sales ~W(PULL)
  @sales_crcs @verified_sales ++ @unverified_sales ++ @pulled_sales
  defp _reduce_source(%__MODULE__{crc: %Crc{crc: crc}} = bc, %BakedRecordBySource{} = brbs)
       when crc in @sales_crcs do
    verified_sale? = crc in @verified_sales

    upsells = bc.responses |> Demo.sum_upsells()
    referrals = bc.responses |> Demo.sum_referrals()

    brbs
    |> Map.update(:calls, 0, &_increment/1)
    |> Map.update(:contacts, 0, &_increment/1)
    |> Map.update(:verified_sales, 0, &_increment?(&1, verified_sale?))
    |> Map.update(:unverified_sales, 0, &_increment?(&1, bc.crc.crc in @unverified_sales))
    |> Map.update(:vsqps, 0, &_increment?(&1, bc.crc.crc in @vsqps))
    |> Map.update(:warnings, 0, &_increment?(&1, bc.crc.crc in @warnings))
    |> Map.update(:unchecked_sales, 0, &_increment?(&1, bc.crc.crc in @unchecked_sales))
    |> Map.update(:pulls, 0, &_increment?(&1, bc.crc.crc in @pulled_sales))
    #Increment if pulled sale and pull_code is NS
    |> Map.update(:ns_pulls, 0, &_increment?(&1, bc.crc.crc in @pulled_sales and bc.dial.pull_code == "NS"))
    |> Map.update(:total_add_value, 0, &_add?(&1, bc.log.add_value, verified_sale?))
    |> Map.update(:upsells, 0, &_add?(&1, upsells, verified_sale?))
    |> Map.update(:referrals, 0, &_add?(&1, referrals, verified_sale?))
    # Don't count pulls, we'll recall them.
    |> Map.update(
      :leads_used,
      0,
      &_increment?(&1, bc.crc.crc in (@verified_sales ++ @unverified_sales))
    )
    |> _append_time(bc)
    |> _count_max_attempts(bc)
    |> _calculate_emails(bc)
  end

  @wrong_number_crcs ~W(DW OI FAX ADC)
  @dnc_crcs ~W(DNC DNCI DNC-B DNCJA DNCSC)
  # Or like NI%
  @not_interested_crcs ~W(NI DNQ NLII NTFS NOR)
  @negative_crcs @wrong_number_crcs ++ @dnc_crcs ++ @not_interested_crcs
  defp _reduce_source(%__MODULE__{crc: %Crc{crc: crc}} = bc, %BakedRecordBySource{} = brbs)
       when crc in @negative_crcs do
    brbs
    |> Map.update(:calls, 0, &_increment/1)
    |> Map.update(:contacts, 0, &_increment?(&1, bc.crc.contact))
    |> _calculate_leads_used(bc)
    |> Map.update(:wrong_numbers, 0, &_increment?(&1, crc in @wrong_number_crcs))
    |> Map.update(:dnc_numbers, 0, &_increment?(&1, crc in @dnc_crcs))
    |> Map.update(
      :not_interested_count,
      0,
      &_increment?(&1, String.starts_with?(crc, "NI") or crc in @not_interested_crcs)
    )
    |> _append_time(bc)
    |> _count_max_attempts(bc)
    |> _calculate_max_attempts_non_final(bc)
  end

  defp _reduce_source(
         %__MODULE__{crc: %Crc{crc: "NI" <> _ = crc}} = bc,
         %BakedRecordBySource{} = brbs
       ) do
    brbs
    |> Map.update(:calls, 0, &_increment/1)
    |> Map.update(:contacts, 0, &_increment?(&1, bc.crc.contact))
    |> _calculate_leads_used(bc)
    |> Map.update(
      :not_interested_count,
      0,
      &_increment?(&1, String.starts_with?(crc, "NI") or crc in @not_interested_crcs)
    )
    |> _append_time(bc)
    |> _count_max_attempts(bc)
    |> _calculate_max_attempts_non_final(bc)
  end

  defp _reduce_source(%__MODULE__{} = bc, %BakedRecordBySource{} = brbs) do
    brbs
    |> Map.update(:calls, 0, &_increment/1)
    |> Map.update(:contacts, 0, &_increment?(&1, bc.crc.contact))
    |> _calculate_leads_used(bc)
    |> _append_time(bc)
    |> _count_max_attempts(bc)
    |> _calculate_max_attempts_non_final(bc)
  end

  defp _reduce_responses(%BakedRecordBySource{} = brbs, []), do: brbs

  defp _reduce_responses(%BakedRecordBySource{} = brbs, components) when is_list(components) do
    components
    |> Enum.reduce(brbs, &_reduce_component/2)
  end

  @spec _reduce_component(bc :: __MODULE__.t(), brbs :: BakedRecordBySource.t()) ::
          BakedRecordbySource.t()
  defp _reduce_component(bc, brbs) do
    brbs
    |> _count_demos(bc)
    |> _pivot_demos(bc)
  end

  ## Begin countable responses
  defp _count_demos(brbs, bc) do
    bc.responses
    |> Enum.filter(&Demo.countable?/1)
    |> Enum.reduce(brbs, &_count_demo/2)
  end

  # ReferalFName
  defp _count_demo(%Demo{question_name: "ReferalF" <> _} = demo, %BakedRecordBySource{} = brbs) do
    brbs
    |> Map.update(:referral_count, 0, &_increment?(&1, String.trim(demo.response) != ""))
  end

  # ReferalName
  defp _count_demo(%Demo{question_name: "ReferalN" <> _} = demo, %BakedRecordBySource{} = brbs) do
    brbs
    |> Map.update(:referral_count, 0, &_increment?(&1, String.trim(demo.response) != ""))
  end

  # ReplacementFName
  defp _count_demo(
         %Demo{question_name: "ReplacementF" <> _} = demo,
         %BakedRecordBySource{} = brbs
       ) do
    brbs
    |> Map.update(:replacement_count, 0, &_increment?(&1, String.trim(demo.response) != ""))
  end

  # ReplacementName
  defp _count_demo(
         %Demo{question_name: "ReplacementN" <> _} = demo,
         %BakedRecordBySource{} = brbs
       ) do
    brbs
    |> Map.update(:replacement_count, 0, &_increment?(&1, String.trim(demo.response) != ""))
  end

  defp _count_demo(%Demo{}, brbs), do: brbs
  defp _count_demo(nil, brbs), do: brbs
  ## End countable responses

  ## Begin pivotable responses
  defp _pivot_demos(brbs, bc) do
    # All (relevant) actual responses
    new_responses =
      bc.responses
      # Only pivotable responses
      |> Enum.filter(&Demo.pivotable?/1)
      # Normalize them
      |> Enum.map(&Demo.normalize_response/1)
      # Pivot them
      |> Enum.reduce(%{}, &_pivot_demo/2)
      # Turn them into BakedResponses
      |> Enum.map(fn {question_name, values} ->
        values
        |> Enum.map(fn {value, count} ->
          %BakedResponse{
            question_name: question_name,
            value: value,
            count: count
          }
        end)
      end)
      # Flatten the list
      |> List.flatten()

    %BakedRecordBySource{
      brbs
      | # They are the *new* responses
        baked_responses: brbs |> _merge_responses(new_responses)
    }
  end

  defp _pivot_demo(%Demo{question_name: question_name, response: value}, %{} = acc) do
    normalized_value = _normalize_response(value)

    values = Map.get(acc, question_name, %{}) |> Map.update(normalized_value, 1, &_increment/1)
    Map.put(acc, question_name, values)
  end

  defp _merge_responses(%BakedRecordBySource{} = brbs, new_responses) do
    BakedResponse.merge(brbs.baked_responses, new_responses)
  end

  defp _calculate_emails(%BakedRecordBySource{} = brbs, %__MODULE__{
         crc: crc,
         contact: c,
         responses: [_ | _] = responses
       }) do
    current_email = c.email |> _normalize_email()

    new_email =
      responses
      |> Enum.find(%Demo{}, &(&1.question_name == "NewEmail"))
      |> Map.get(:response)
      |> _normalize_email()

    brbs
    |> Map.update(:email_count, 0, &_increment?(&1, crc.success and new_email != ""))
    |> Map.update(
      :new_email_count,
      0,
      &_increment?(&1, crc.success and new_email != "" and current_email == "")
    )
    |> Map.update(
      :updated_email_count,
      0,
      &_increment?(
        &1,
        crc.success and new_email != "" and new_email != current_email and current_email != ""
      )
    )
    |> Map.update(
      :lost_email_count,
      0,
      &_increment?(&1, crc.success and new_email == "" and current_email != "")
    )
    |> Map.update(
      :incoming_blank_email_count,
      0,
      &_increment?(&1, crc.success and current_email == "")
    )
  end

  defp _calculate_emails(brbs, _baked_component) do
    brbs
  end

  ## End pivotable responses

  # END: Action reduction

  defp _calculate_work_hours(%BakedRecordBySource{work_seconds: x} = brbs) when x in [nil, 0] do
    %BakedRecordBySource{
      brbs
      | work_hours: 0
    }
  end

  defp _calculate_work_hours(%BakedRecordBySource{work_seconds: seconds} = brbs) do
    %BakedRecordBySource{
      brbs
      | work_hours: seconds / 3600
    }
  end

  defp _calculate_revenue(%{pricing_method: "Unpriced"} = brbs) do
    %BakedRecordBySource{
      brbs
      | revenue: 0
    }
  end

  defp _calculate_revenue(
         %BakedRecordBySource{
           pricing_method: method,
           rate: rate,
           verified_sales: verified_sales,
           total_add_value: total_add_value
         } =
           brbs
       )
       when method in ~w(PerOrder PerSourceOrder) do
    %BakedRecordBySource{
      brbs
      | revenue: (rate * verified_sales) + (total_add_value || 0)
    }
  end

  defp _calculate_revenue(
         %BakedRecordBySource{pricing_method: "PerHour", rate: rate, work_hours: work_hours} =
           brbs
       ) do
    %BakedRecordBySource{
      brbs
      | revenue: rate * work_hours
    }
  end

  defp _append_time(%BakedRecordBySource{} = brbs, %__MODULE__{} = baked_component) do
    work_time = _work_time(baked_component)

    if baked_component.log.source_id == "FPT2406RQ1" do
      IO.puts(
        "Appending time: #{work_time} to #{inspect(baked_component.log.dial_id)} via #{inspect(baked_component.log.agent_log_id)}, w/ #{inspect(Map.get(baked_component.group_info || %{}, :total_time))} & #{inspect(Map.get(baked_component.group_info || %{}, :total_time_per_container))}"
      )
    end

    %BakedRecordBySource{
      brbs
      | work_seconds: brbs.work_seconds + work_time
    }
  end

  def _work_time(%__MODULE__{group_info: group_info})
      when group_info != false and not is_nil(group_info) do
    group_info |> Map.get(:total_time_per_container, 0)
  end

  def _work_time(%__MODULE__{log: %History{total_time: 0, connect_time: connect_time}})
      when connect_time > 0 do
    connect_time
  end

  def _work_time(%__MODULE__{} = baked_component) do
    baked_component.log.total_time || 0
  end

  defp _count_max_attempts(%BakedRecordBySource{} = brbs, %__MODULE__{
         log: log,
         max_attempts: max_attempts
       }) do
    brbs
    |> Map.update(:max_attempts, 0, &_increment?(&1, log.attempt >= max_attempts))
  end

  defp _calculate_leads_used(%BakedRecordBySource{} = brbs, %__MODULE__{crc: crc}) do
    brbs
    |> Map.update(:leads_used, 0, &_increment?(&1, crc.final_crc))
  end

  defp _calculate_max_attempts_non_final(%BakedRecordBySource{} = brbs, %__MODULE__{
         crc: crc,
         log: log,
         max_attempts: max_attempts
       }) do
    brbs
    |> Map.update(
      :max_attempts_non_final,
      0,
      &_increment?(&1, not crc.final_crc and log.attempt >= max_attempts)
    )
  end

  defp _increment(nil), do: 1
  defp _increment(n) when is_integer(n), do: n + 1
  defp _increment(_), do: raise("Invalid number")

  defp _increment?(n, true), do: _increment(n)
  defp _increment?(n, _), do: n

  defp _add(nil, n), do: n
  defp _add(n, nil), do: n
  defp _add(n1, n2), do: n1 + n2

  defp _add?(n1, n2, true), do: _add(n1, n2)
  defp _add?(n1, _n2, _), do: n1

  defp _normalize_response(nil), do: "<blank>"
  defp _normalize_response(""), do: "<blank>"
  defp _normalize_response(response), do: response

  defp _normalize_email(nil), do: ""

  defp _normalize_email(email) when is_binary(email) do
    email
    |> String.trim()
    |> String.downcase()
  end

  defp _normalize_email(_), do: ""
end
