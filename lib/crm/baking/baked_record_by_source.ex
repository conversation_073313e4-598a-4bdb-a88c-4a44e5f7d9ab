defmodule Crm.Baking.BakedRecordBySource do
  use Ecto.Schema
  import Ecto.Changeset

  alias Crm.Baking.BakedResponse

  @moduledoc """
  This module provides the schema and common functions for the BakedRecords.
  """
  @primary_key {:bake_source_id, :id, autogenerate: true, source: :BakeSourceID}
  schema "BakedRecordsBySource" do
    field :bake_id, :integer, source: :BakeID

    field :source_id, :string, source: :SourceID

    field :calls, :integer, default: 0
    field :contacts, :integer, default: 0
    field :verified_sales, :integer, source: :VerifiedSales, default: 0
    field :unverified_sales, :integer, source: :UnverifiedSales, default: 0
    field :vsqps, :integer, source: :VSQPs, default: 0
    field :warnings, :integer, source: :warnings, default: 0
    field :unchecked_sales, :integer, source: :UncheckedSales, default: 0
    field :pulls, :integer, default: 0
    field :ns_pulls, :integer, source: :NSPulls, default: 0
    field :upsells, :integer, default: 0
    field :referrals, :integer, default: 0
    field :total_add_value, :float, source: :TotalAddValue, default: 0.0

    field :pricing_method, :string, source: :PricingMethod, default: "None"
    field :rate, :float, default: 0.0

    # Work seconds aims to be the time that the user is actively working on the project, whether they are logged into that system or not.
    # However, it's currently under reported from the users production system.
    field :work_seconds, :integer, source: :WorkSeconds, default: 0
    field :work_hours, :float, source: :WorkHours, default: 0.0

    # System seconds is the time as reported from the Dialer, App, Website, or other system that the user logs into.
    field :system_seconds, :integer, source: :SystemSeconds, default: 0
    field :system_hours, :float, source: :SystemHours, default: 0.0

    field :revenue, :float, default: 0.0

    # Audience Development Fields
    field :leads_used, :integer, source: :LeadsUsed, default: 0
    field :wrong_numbers, :integer, source: :WrongNumbers, default: 0
    field :dnc_numbers, :integer, source: :DNCNumbers, default: 0
    field :not_interested_count, :integer, source: :NICount, default: 0
    field :max_attempts, :integer, source: :MaxAttempts, default: 0
    field :max_attempts_non_final, :integer, source: :MaxAttemptsNonFinal, default: 0

    # Email Fields
    field :email_count, :integer, source: :EmailCount, default: 0
    field :new_email_count, :integer, source: :NewEmailCount, default: 0
    field :updated_email_count, :integer, source: :UpdatedEmailCount, default: 0
    field :lost_email_count, :integer, source: :LostEmailCount, default: 0
    field :incoming_blank_email_count, :integer, source: :IncomingBlankEmailCount, default: 0

    ## Response based metrics
    field :referral_count, :integer, source: :ReferralCount, default: 0
    field :replacement_count, :integer, source: :ReplacementCount, default: 0

    has_many :baked_responses, BakedResponse,
      foreign_key: :bake_source_id,
      references: :bake_source_id,
      on_delete: :delete_all,
      on_replace: :delete_if_exists
  end

  def changeset(brbs, %{source_id: source} = attrs) when source in [nil, ""] do
    brbs
    |> changeset(%{attrs | source_id: "Unsourced"})
  end

  def changeset(baked_source_record, attrs) do
    baked_source_record
    |> cast(attrs, keys(attrs))
    |> cast_assoc(:baked_responses)
    |> unique_constraint(
      [
        :bake_id,
        :source_id
      ],
      name: "bakedrecordsbysource.bake_source"
    )
  end

  def changeset(brbs, attrs, baked_responses) do
    brbs
    |> changeset(attrs)
    |> put_assoc(:baked_responses, baked_responses)
  end

  @rejected_keys [:id, :bake_source_id, :baked_responses]
  def keys(attrs) do
    Map.keys(attrs)
    |> Enum.reject(fn
      id when is_atom(id) ->
        id in @rejected_keys

      id when is_binary(id) ->
        String.to_existing_atom(id) in @rejected_keys
    end)
  end
end
