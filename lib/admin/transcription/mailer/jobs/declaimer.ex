defmodule Admin.Transcription.Mailer.Jobs.Declaimer do
  @moduledoc """
  This recursive Oban job is responsible for de-claiming mailers that are not actively being worked on.

  This is a live process that will send a broadcast on the mailers channel, and de-claim the mailer if it does not receive a response.

  See `Admin.Transcription.Mailer` for more information.
  """
  use Admin.Jobs.RecursiveJob,
    # Wait 2 minutes between declaiming runs
    # This provides an absolute minimum claim age to
    # account for network troubles.
    schedule_in: 120,
    conflict_sleep_delay: 15,
    # 60 minutes
    max_runtime_minutes: 1_200

  import Ecto.Query, warn: false

  alias Admin.Jobs.RecursiveJobOptions
  alias Admin.Transcription.Mailer
  alias Admin.AdminRepo, as: Repo

  require Logger

  @impl true
  def fetch_batch(%RecursiveJobOptions{limit: limit, schedule_in: cut_off_seconds}) do
    # Don't de-claim all mailers, only de-claim mailers that were claimed before
    # the last job started.
    # Otherwise, the de-claim job would potentually de-claim a mailer that was
    # claimed the previous second.
    from(m in Mailer,
      where: m.status == :claimed and m.updated_at < ago(^cut_off_seconds, "second"),
      select: m.id,
      order_by: [desc: m.id],
      limit: ^limit
    )
    |> Repo.all()
  end

  @impl true
  def perform_batch(_job, _job_options, batch) do
    # Enhanced declaiming with better coordination
    # Send active? messages with a unique batch ID to track responses better
    batch_id = :crypto.strong_rand_bytes(8) |> Base.encode64()

    batch
    |> Enum.map(fn id ->
      Logger.debug("Declaimer sending active? for #{id} (batch: #{batch_id})")
      Phoenix.PubSub.broadcast(Admin.PubSub, "mailers:#{id}", {:active?, id, batch_id, self()})
    end)

    # Wait longer and be more specific about responses
    {:ok, messages} = consume_messages_for(:timer.seconds(7))

    # Filter responses to only this batch
    valid_responses =
      messages
      |> Enum.filter(fn
        {id, true, ^batch_id} when id in batch -> true
        _ -> false
      end)
      |> Enum.map(fn {id, true, _} -> id end)

    declaimable =
      batch
      |> Enum.reject(fn id ->
        id in valid_responses
      end)

    Logger.info("Declaimer batch #{batch_id}: checked #{length(batch)}, active #{length(valid_responses)}, declaiming #{length(declaimable)}")

    declaimable
    |> Enum.map(fn id ->
      Logger.debug("Declaimer declaiming #{id}")
      id
      |> Mailer.get_by_id!()
      |> Mailer.declaim()
    end)

    :ok
  end

  defp consume_messages_for(total_milliseconds) do
    consume_messages_for([], total_milliseconds)
  end

  defp consume_messages_for(messages, 0), do: {:ok, messages}

  defp consume_messages_for(messages, remaining_milliseconds) do
    time_start = System.os_time(:millisecond)

    receive do
      message ->
        Logger.debug("Declaimer received message: #{inspect(message)}")
        diff = System.os_time(:millisecond) - time_start
        consume_messages_for([message | messages], (remaining_milliseconds - diff) |> max(0))
    after
      remaining_milliseconds ->
        {:ok, messages}
    end
  end
end
