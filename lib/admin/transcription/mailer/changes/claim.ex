defmodule Admin.Transcription.Mailer.Changes.Claim do
  @moduledoc """
  This module is responsible for handling the claim change to the Mailer resource.

  It is used to change the status of a Mailer record to the :claimed status, and set the old status to the previous status.

  NOTE: This process should be revised.
  """
  # FIXME: Migrate the claiming system to use it's own `claimed_by_id` rather than setting the status to something else.
  use Ash.Resource.Change

  alias Ash.Changeset

  def change(%Changeset{} = changeset, _opts, context) do
    case Changeset.get_attribute(changeset, :status) do
      :claimed ->
        # already claimed
        changeset

      old_status ->
        updated_changeset =
          changeset
          |> Changeset.change_attribute(:old_status, old_status)
          |> Changeset.change_attribute(:status, :claimed)

        # Broadcast that this mailer was claimed
        if context.actor do
          mailer_id = Changeset.get_attribute(changeset, :id)
          claimer_info = %{
            id: context.actor.id,
            name: context.actor.name || context.actor.email
          }

          Phoenix.PubSub.broadcast(
            Admin.PubSub,
            "mailers:#{mailer_id}",
            {:mailer_claimed, mailer_id, claimer_info}
          )
        end

        updated_changeset
    end
  end
end
