defmodule Admin.Transcription.Mailer.Preperations do
  @moduledoc """
  This module is responsible for handling the preparations for the Mailer resource.

  It is used to set the search filter for the Mailer resource, and to set the simple search filter for the Mailer resource.
  """
  require Ash.Query
  alias Ash.Changeset
  import Ash.Query

  @date_format "{YYYY}-{0M}-{0D}"
  defp date_format, do: @date_format

  def set_search_filter(query, _context) do
    query
    |> _exception_filter()
    |> _status_filter()
    |> _form_id_filter()
    |> _time_filters()
    |> _user_filters()
  end

  def set_simple_search_filter(query, _context) do
    query
    |> _status_filter()
    |> _form_id_filter()
  end

  def set_simple_search_filter_with_lock(query, context) do
    alias Admin.AdminRepo

    # Use a database transaction with row-level locking
    # This works well with the existing declaiming system because:
    # 1. Locks are short-lived (only during assignment)
    # 2. Declaiming system continues to work as a safety net
    # 3. No state synchronization issues
    AdminRepo.transaction(fn ->
      # First, find and lock the next available mailer
      # Use FOR UPDATE SKIP LOCKED to avoid blocking on claimed mailers
      locked_mailer = query
      |> _status_filter()
      |> _form_id_filter()
      |> filter(status != :claimed)  # Exclude already claimed mailers
      |> Ash.Query.limit(1)
      |> Ash.Query.lock("FOR UPDATE SKIP LOCKED")  # Skip locked rows, prevents blocking
      |> Ash.read_one!()

      case locked_mailer do
        nil ->
          AdminRepo.rollback({:error, :no_available_mailers})

        mailer ->
          # Immediately claim the mailer within the same transaction
          # This ensures atomicity - either we get the mailer or we don't
          case mailer |> Ash.Changeset.for_update(:claim) |> Ash.update() do
            {:ok, claimed_mailer} ->
              claimed_mailer

            {:error, _} ->
              AdminRepo.rollback({:error, :claim_failed})
          end
      end
    end)
    |> case do
      {:ok, mailer} -> {:ok, mailer}
      {:error, reason} -> {:error, reason}
    end
  end

  defp _exception_filter(query) do
    case query |> Changeset.fetch_argument(:exception) do
      {:ok, true} ->
        query
        |> filter(exception == true)

      {:ok, false} ->
        query
        |> filter(exception == false)

      _ ->
        query
    end
  end

  defp _status_filter(query) do
    case query |> Changeset.fetch_argument(:status) do
      {:ok, "exception"} ->
        query
        |> filter(status == :previewed and exception == true)

      {:ok, status} when not is_nil(status) ->
        query
        |> filter(status == ^status)

      _ ->
        query
    end
  end

  defp _form_id_filter(query) do
    case query |> Changeset.fetch_argument(:form_id) do
      {:ok, form_id} when not is_nil(form_id) ->
        query
        |> filter(form_id == ^form_id)

      _ ->
        query
    end
  end

  defp _time_filters(query) do
    {date_lower, date_upper} = query |> __fetch_time_range()

    date_upper = date_upper |> ___add_day()

    case query |> Changeset.fetch_argument(:time_target) do
      {:ok, "preview_datetime"} ->
        query
        |> filter(
          (preview_datetime >= ^date_lower and
             preview_datetime <= ^date_upper) or
            is_nil(preview_datetime)
        )

      {:ok, "transcribe_datetime"} ->
        query
        |> filter(
          (transcribe_datetime >= ^date_lower and
             transcribe_datetime <= ^date_upper) or
            is_nil(transcribe_datetime)
        )

      {:ok, "review_datetime"} ->
        query
        |> filter(
          (review_datetime >= ^date_lower and
             review_datetime <= ^date_upper) or
            is_nil(review_datetime)
        )

      _ ->
        query
    end
  end

  defp __fetch_time_range(query) do
    {:ok, date_lower} = query |> Changeset.fetch_argument(:date_lower)
    {:ok, date_upper} = query |> Changeset.fetch_argument(:date_upper)

    {:ok, date_lower} = date_lower |> Timex.parse(date_format())
    {:ok, date_upper} = date_upper |> Timex.parse(date_format())

    {date_lower, date_upper}
  rescue
    _e in MatchError ->
      {:ok, today} = Timex.local() |> Timex.format!(date_format()) |> Timex.parse(date_format())
      {today, today}
  end

  defp ___add_day(%NaiveDateTime{} = date) do
    date
    |> NaiveDateTime.add(1, :day)
  end

  defp _user_filters(query) do
    case query |> Changeset.fetch_argument(:actor_id) do
      {:ok, id} ->
        case query |> Changeset.fetch_argument(:time_target) do
          {:ok, "preview_datetime"} ->
            query
            |> filter(previewed_by_id == ^id)

          {:ok, "transcribe_datetime"} ->
            query
            |> filter(transcribed_by_id == ^id)

          {:ok, "review_datetime"} ->
            query
            |> filter(reviewed_by_id == ^id)

          _ ->
            # If we didn't set the time_target, we can't assume...
            query
        end

      _ ->
        query
    end
  end
end
