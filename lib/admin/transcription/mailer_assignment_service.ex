defmodule Admin.Transcription.MailerAssignmentService do
  @moduledoc """
  Centralized service for managing mailer assignments to prevent duplicate assignments.
  
  This GenServer maintains state of who is working on what and ensures atomic assignment
  of mailers to transcribers.
  """
  
  use GenServer
  require Logger
  
  alias Admin.Transcription.Mailer
  alias Admin.Accounts.User
  
  # Client API
  
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end
  
  @doc """
  Request the next available mailer for a user with given criteria.
  Returns {:ok, mailer} or {:error, reason}
  """
  def request_next_mailer(user_id, status, form_id \\ nil) do
    GenServer.call(__MODULE__, {:request_next_mailer, user_id, status, form_id}, 10_000)
  end
  
  @doc """
  Release a mailer when a user is done with it.
  """
  def release_mailer(user_id, mailer_id) do
    GenServer.cast(__MODULE__, {:release_mailer, user_id, mailer_id})
  end
  
  @doc """
  Get current assignments for reporting/debugging.
  """
  def get_assignments do
    GenServer.call(__MODULE__, :get_assignments)
  end
  
  # Server Implementation
  
  @impl true
  def init(_) do
    # State: %{user_id => %{mailer_id: mailer_id, assigned_at: timestamp}}
    {:ok, %{assignments: %{}, mailer_locks: %{}}}
  end
  
  @impl true
  def handle_call({:request_next_mailer, user_id, status, form_id}, _from, state) do
    case find_and_assign_next_mailer(user_id, status, form_id, state) do
      {:ok, mailer, new_state} ->
        {:reply, {:ok, mailer}, new_state}
        
      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end
  
  @impl true
  def handle_call(:get_assignments, _from, state) do
    {:reply, state.assignments, state}
  end
  
  @impl true
  def handle_cast({:release_mailer, user_id, mailer_id}, state) do
    new_assignments = Map.delete(state.assignments, user_id)
    new_locks = Map.delete(state.mailer_locks, mailer_id)
    
    new_state = %{state | assignments: new_assignments, mailer_locks: new_locks}
    {:noreply, new_state}
  end
  
  # Private functions
  
  defp find_and_assign_next_mailer(user_id, status, form_id, state) do
    # Release any previous assignment for this user
    state = release_user_assignment(user_id, state)
    
    # Find next available mailer
    case Mailer.next(status, form_id) do
      {:ok, mailer} ->
        # Check if this mailer is already locked by another user
        if Map.has_key?(state.mailer_locks, mailer.id) do
          # Try to find another mailer
          find_alternative_mailer(user_id, status, form_id, state, [mailer.id])
        else
          # Claim the mailer
          case Mailer.claim(mailer, actor: Ash.get!(User, user_id)) do
            {:ok, claimed_mailer} ->
              new_assignments = Map.put(state.assignments, user_id, %{
                mailer_id: mailer.id,
                assigned_at: System.monotonic_time(:second)
              })
              new_locks = Map.put(state.mailer_locks, mailer.id, user_id)
              
              new_state = %{state | assignments: new_assignments, mailer_locks: new_locks}
              {:ok, claimed_mailer, new_state}
              
            {:error, _} ->
              # Claiming failed, try to find another
              find_alternative_mailer(user_id, status, form_id, state, [mailer.id])
          end
        end
        
      {:error, _} ->
        {:error, :no_available_mailers}
    end
  end
  
  defp find_alternative_mailer(user_id, status, form_id, state, excluded_ids, attempts \\ 0) do
    if attempts >= 5 do
      {:error, :all_mailers_claimed}
    else
      # Get multiple mailers and find one not in our exclusion list
      case get_multiple_mailers(status, form_id, 10) do
        [] ->
          {:error, :no_available_mailers}
          
        mailers ->
          available_mailer = Enum.find(mailers, fn m -> 
            not Map.has_key?(state.mailer_locks, m.id) and m.id not in excluded_ids
          end)
          
          case available_mailer do
            nil ->
              # All found mailers are locked, try again
              new_excluded = excluded_ids ++ Enum.map(mailers, & &1.id)
              find_alternative_mailer(user_id, status, form_id, state, new_excluded, attempts + 1)
              
            mailer ->
              # Try to claim this mailer
              case Mailer.claim(mailer, actor: Ash.get!(User, user_id)) do
                {:ok, claimed_mailer} ->
                  new_assignments = Map.put(state.assignments, user_id, %{
                    mailer_id: mailer.id,
                    assigned_at: System.monotonic_time(:second)
                  })
                  new_locks = Map.put(state.mailer_locks, mailer.id, user_id)
                  
                  new_state = %{state | assignments: new_assignments, mailer_locks: new_locks}
                  {:ok, claimed_mailer, new_state}
                  
                {:error, _} ->
                  # This mailer was claimed by someone else, try another
                  new_excluded = [mailer.id | excluded_ids]
                  find_alternative_mailer(user_id, status, form_id, state, new_excluded, attempts + 1)
              end
          end
      end
    end
  end
  
  defp release_user_assignment(user_id, state) do
    case Map.get(state.assignments, user_id) do
      nil ->
        state
        
      %{mailer_id: mailer_id} ->
        new_assignments = Map.delete(state.assignments, user_id)
        new_locks = Map.delete(state.mailer_locks, mailer_id)
        %{state | assignments: new_assignments, mailer_locks: new_locks}
    end
  end
  
  defp get_multiple_mailers(status, form_id, limit) do
    # This would need to be implemented as a new Ash action
    # For now, we'll use the existing next function multiple times
    # In a real implementation, you'd want a more efficient query
    []
  end
end
