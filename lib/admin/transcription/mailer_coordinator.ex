defmodule Admin.Transcription.MailerCoordinator do
  @moduledoc """
  Improved PubSub-based coordination for mailer assignments.
  
  This module provides a more robust PubSub implementation that ensures
  proper coordination between multiple transcribers to prevent duplicate
  mailer assignments.
  """
  
  alias Phoenix.PubSub
  alias Admin.Transcription.Mailer
  alias Admin.Accounts.User
  
  @coordination_topic "mailer_coordination"
  @assignment_timeout 5_000  # 5 seconds
  
  @doc """
  Request the next mailer with coordination to prevent duplicates.
  """
  def request_next_mailer(user_id, status, form_id \\ nil) do
    request_id = generate_request_id()
    
    # Subscribe to coordination topic
    PubSub.subscribe(Admin.PubSub, @coordination_topic)
    
    # Broadcast intent to get next mailer
    PubSub.broadcast(Admin.PubSub, @coordination_topic, {
      :mailer_request, 
      request_id, 
      user_id, 
      status, 
      form_id, 
      self()
    })
    
    # Wait for coordination responses
    case coordinate_assignment(request_id, user_id, status, form_id) do
      {:ok, mailer} ->
        PubSub.unsubscribe(Admin.PubSub, @coordination_topic)
        {:ok, mailer}
        
      {:error, reason} ->
        PubSub.unsubscribe(Admin.PubSub, @coordination_topic)
        {:error, reason}
    end
  end
  
  @doc """
  Handle coordination messages in LiveView processes.
  Call this from handle_info in your LiveView.
  """
  def handle_coordination_message({:mailer_request, request_id, requesting_user_id, status, form_id, reply_to}, socket) do
    current_user_id = socket.assigns.current_user.id
    current_mailer = socket.assigns.current
    
    # Don't respond to our own requests
    if requesting_user_id != current_user_id do
      response = %{
        user_id: current_user_id,
        current_mailer_id: if(current_mailer, do: current_mailer.id, else: nil),
        session_type: socket.assigns.session_type,
        timestamp: System.monotonic_time(:millisecond)
      }
      
      send(reply_to, {:coordination_response, request_id, response})
    end
    
    socket
  end
  
  def handle_coordination_message({:mailer_assignment, request_id, user_id, mailer_id}, socket) do
    # Another user got assigned a mailer, update our local state if needed
    if socket.assigns.current && socket.assigns.current.id == mailer_id do
      # Someone else got "our" mailer, we need to get a new one
      socket
      |> Phoenix.LiveView.put_flash(:warning, "Mailer was assigned to another user. Getting next available mailer...")
    else
      socket
    end
  end
  
  def handle_coordination_message(_, socket), do: socket
  
  # Private functions
  
  defp coordinate_assignment(request_id, user_id, status, form_id) do
    # Collect responses from other active transcribers
    responses = collect_coordination_responses(request_id, 2000)  # 2 second timeout
    
    # Analyze responses to find conflicts
    case find_available_mailer(user_id, status, form_id, responses) do
      {:ok, mailer} ->
        # Broadcast that we got this mailer
        PubSub.broadcast(Admin.PubSub, @coordination_topic, {
          :mailer_assignment, 
          request_id, 
          user_id, 
          mailer.id
        })
        {:ok, mailer}
        
      {:error, reason} ->
        {:error, reason}
    end
  end
  
  defp collect_coordination_responses(request_id, timeout) do
    collect_responses(request_id, [], timeout, System.monotonic_time(:millisecond))
  end
  
  defp collect_responses(request_id, responses, remaining_timeout, start_time) do
    if remaining_timeout <= 0 do
      responses
    else
      receive do
        {:coordination_response, ^request_id, response} ->
          elapsed = System.monotonic_time(:millisecond) - start_time
          new_timeout = max(0, remaining_timeout - elapsed)
          collect_responses(request_id, [response | responses], new_timeout, start_time)
          
        _ ->
          # Ignore other messages
          elapsed = System.monotonic_time(:millisecond) - start_time
          new_timeout = max(0, remaining_timeout - elapsed)
          collect_responses(request_id, responses, new_timeout, start_time)
      after
        remaining_timeout ->
          responses
      end
    end
  end
  
  defp find_available_mailer(user_id, status, form_id, responses) do
    # Get potential mailers
    potential_mailers = get_potential_mailers(status, form_id, 5)
    
    # Find mailers that are not currently being worked on
    occupied_mailer_ids = 
      responses
      |> Enum.map(& &1.current_mailer_id)
      |> Enum.reject(&is_nil/1)
      |> MapSet.new()
    
    available_mailer = 
      potential_mailers
      |> Enum.find(fn mailer ->
        not MapSet.member?(occupied_mailer_ids, mailer.id)
      end)
    
    case available_mailer do
      nil ->
        {:error, :no_available_mailers}
        
      mailer ->
        # Try to claim it
        case Mailer.claim(mailer, actor: Ash.get!(User, user_id)) do
          {:ok, claimed_mailer} ->
            {:ok, claimed_mailer}
            
          {:error, _} ->
            # Someone else claimed it, try next available
            remaining_mailers = potential_mailers -- [mailer]
            find_available_from_list(remaining_mailers, occupied_mailer_ids, user_id)
        end
    end
  end
  
  defp find_available_from_list([], _occupied_ids, _user_id) do
    {:error, :all_mailers_claimed}
  end
  
  defp find_available_from_list([mailer | rest], occupied_ids, user_id) do
    if MapSet.member?(occupied_ids, mailer.id) do
      find_available_from_list(rest, occupied_ids, user_id)
    else
      case Mailer.claim(mailer, actor: Ash.get!(User, user_id)) do
        {:ok, claimed_mailer} ->
          {:ok, claimed_mailer}
          
        {:error, _} ->
          find_available_from_list(rest, occupied_ids, user_id)
      end
    end
  end
  
  defp get_potential_mailers(status, form_id, limit) do
    # This would need to be implemented as a new Ash action that returns multiple mailers
    # For now, we'll simulate by calling next multiple times
    # In a real implementation, you'd want a single efficient query
    
    case Mailer.next(status, form_id) do
      {:ok, mailer} -> [mailer]
      {:error, _} -> []
    end
  end
  
  defp generate_request_id do
    :crypto.strong_rand_bytes(16) |> Base.encode64()
  end
end
