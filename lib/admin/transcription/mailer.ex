defmodule Admin.Transcription.Mailer do
  @moduledoc """
  This `Ash.Resource` represents a mailer that was filled out and mailed by a customer of one of our clients. That mailer is recieved by a vendor that we partner with, and scanned in bulk along with other mailers. Each "type" of mailer is charicterized by a `Form`, referenced by the `source` field on the `Mailer`. This `source` field is _attempted_ to be parsed via OCR during the import process.

  ## Handling

  The `/transcription` endpoint of the router offers multiple tools to aid in data entry and quality assurance.

  ### Form editor

  The form editor is used to create and manage the `Form` records that are used to characterize each type of mailer. This is where you will define the fields that are present on the mailer, and the type of each field, as well as their "hint" positions (a marker for the field helping the agent locate it on the scanned mailer.)

  See `Admin.Transcription.Form` for more information.

  ### Data Entry

  The data entry page is used to transcribe the data from the mailers into the system. This is where the Data Entry agents will spend the majority of their time. The page is designed to be as simple and as fast as possible to allow the agents to work as efficiently as possible.

  It has two views, *Preview* and *Full Form*. The Preview view has very basic contact information, and is used to quickly filter out mailers that are not relevant to the agent. The Full Form view has all of the fields for the mailer, and is used to transcribe the "core data", mainly to correct a bad OCR reading of source code, and verify the account number. While the Full Form has all the fields from the `Admin.Transcription.Form`.

  See `Admin.Transcription.TranscribeLive.Index` for more information.

  ### Quality Assurance

  The quality assurance page is used to review the data that has been entered by the agents. This is where the QC agents will spend the majority of their time. The page is designed to be as simple and as fast as possible to allow the agents to work as efficiently as possible.

  See `Admin.Transcription.ReviewLive.Index` for more information.


  ## Internals

  ### Claiming System

  The claiming system is designed to allow multiple agents to work on the same set of mailers without stepping on each others toes. It does this by following a set of operations on the `TranscribeLive` page, and is backed up by a De-Claiming Oban Worker that runs every few minutes (See `Admin.Transcription.Mailer.Jobs.Declaimer`).

  Sequence:

  1. Agent loads a mailer, the `TranscribeLive` page will "claim" a mailer. That mailer is then excluded from the pool of available mailers.
  2. `TranscribeLive` page will subscribe to the mailers `PubSub` channel (`mailer:<mailer id>`.)
  2. As the agent works on the mailer, the De-Claiming job will run, which will obtain all claimed mailers and performs the following.
    1. Send a `PubSub` broadcast to the mailer's channel for every claimed mailer.
      - (The `TranscribeLive` page will receive this and reply if the user is still on that mailer)
    2. Receive responses for a span of a few seconds.
    3. Match the responses against the batch of claimed mailers.
    4. De-Claim any mailer for which it did not receive a response.
  3. Agent finished or skips the mailer, loads the next mailer.
  4. The `TranscribeLive` page will unsubscribe from the mailers `PubSub` channel.
  5. The entire process repeats.

  This ensures that if an agent suddenly "goes away" (disconnect, page crash, etc.), the mailer will not permanently be claimed.
  """

  use Ash.Resource,
    domain: Admin.Transcription.Domain,
    data_layer: AshPostgres.DataLayer

  require Ash.Query

  alias Admin.Transcription.{Form, Response}
  alias Admin.Transcription.Mailer.{Preperations, Status}
  alias Admin.Transcription.Mailer.Changes.{Review, Claim, Declaim}
  alias Admin.Accounts.User

  code_interface do
    define :next, args: [:status, :form_id], get?: true
    define :next_and_claim_atomic, args: [:status, :form_id], get?: true
    define :claim
    define :declaim
    define :assign, args: [:form_id, :source]
    define :read
    define :get_by_id, args: [:id]
    define :update_account, args: [:account_number]
    define :update_client_notes, args: [:client_notes], action: :update
    define :update_signed_date, args: [:signed_date], action: :update
    define :mark_exception
    define :mark_not_interested
    define :mark_interested
    define :mark_previewed
    define :mark_completed
    define :mark_rejected
    define :mark_reviewed
  end

  resource do
    description "A record of a mailer that was filled out and mailed by a customer. Including scan data."
  end

  postgres do
    table "mailers"
    repo Admin.AdminRepo
  end

  attributes do
    uuid_primary_key :id do
      writable? true
    end

    attribute :audit_id, :integer do
      public? true
      description "The audit id of the mailer, auto incremented"
      writable? true
    end

    attribute :image_url, :string do
      public? true
      description "Location of the scanned mailer"
    end

    attribute :status, Status do
      public? true
      description "Status of the mailer"
      default :new
    end

    attribute :old_status, Status do
      public? true
      description "Status the mailer was before it was claimed"
      default nil
    end

    attribute :original_name, :string do
      public? true
    end

    attribute :source, :string do
      public? true
    end

    attribute :account_number, :string do
      public? true
    end

    attribute :account_number_validated, :boolean do
      public? true
      default false
    end

    attribute :received_date, :date do
      public? true
    end

    attribute :exception, :boolean do
      public? true
      default false
    end

    attribute :not_interested, :boolean do
      public? true
      default false
    end

    attribute :batch_name, :string do
      public? true
    end

    attribute :batch_position, :integer do
      public? true
    end

    attribute :preview_datetime, :naive_datetime do
      public? true
    end

    attribute :transcribe_datetime, :naive_datetime do
      public? true
    end

    attribute :review_datetime, :naive_datetime do
      public? true
    end

    attribute :signed_date, :string do
      public? true
    end

    attribute :qc_notes, :string do
      public? true
    end

    attribute :client_notes, :string do
      public? true
    end

    timestamps()
  end

  relationships do
    belongs_to :form, Form do
      public? true
      attribute_type :uuid
      allow_nil? true
      attribute_writable? true
    end

    has_many :responses, Response do
      public? true
    end

    belongs_to :previewed_by, User do
      public? true
      domain Admin.Domain
      attribute_writable? true
      allow_nil? true
    end

    belongs_to :transcribed_by, User do
      public? true
      domain Admin.Domain
      attribute_writable? true
      allow_nil? true
    end

    belongs_to :reviewed_by, User do
      public? true
      domain Admin.Domain
      attribute_writable? true
      allow_nil? true
    end
  end

  actions do
    defaults [:read, :destroy, create: :*, update: :*]

    read :next do
      get? true
      argument :status, :string

      argument :form_id, :string do
        default nil
      end

      prepare &Preperations.set_simple_search_filter/2

      prepare build(limit: 1, load: [:responses, :form])
    end

    read :next_and_claim_atomic do
      get? true
      argument :status, :string

      argument :form_id, :string do
        default nil
      end

      prepare &Preperations.set_simple_search_filter_with_lock/2

      prepare build(limit: 1, load: [:responses, :form])
    end

    read :get_by_id do
      get? true
      argument :id, :uuid

      filter expr(id == ^arg(:id))

      prepare build(limit: 1, load: [:form, :transcribed_by, :responses])
    end

    update :claim do
      require_atomic? false
      # validate attribute_does_not_equal(:status, :claimed)
      change Claim
      change relate_actor(:previewed_by)
    end

    update :declaim do
      require_atomic? false
      # validate attribute_equals(:status, :claimed)
      change Declaim
      change set_attribute(:previewed_by_id, nil)
    end

    update :update_account do
      require_atomic? false
      argument :account_number, :string
      change set_attribute(:account_number, arg(:account_number))
      change set_attribute(:account_number_validated, true)
    end

    update :mark_previewed do
      require_atomic? false
      change relate_actor(:previewed_by)
      change set_attribute(:status, :previewed)
      change set_attribute(:preview_datetime, &NaiveDateTime.local_now/0)
    end

    update :mark_completed do
      require_atomic? false
      change relate_actor(:transcribed_by)
      change set_attribute(:status, :completed)
      change set_attribute(:transcribe_datetime, &NaiveDateTime.local_now/0)
    end

    update :mark_rejected do
      require_atomic? false
      change relate_actor(:reviewed_by)
      change set_attribute(:status, :rejected)
      change set_attribute(:review_datetime, &NaiveDateTime.local_now/0)
    end

    update :mark_reviewed do
      require_atomic? false
      change relate_actor(:reviewed_by)
      change {Review, []}
      change set_attribute(:review_datetime, &NaiveDateTime.local_now/0)
    end

    update :assign do
      require_atomic? false
      argument :form_id, :uuid

      accept [:source]

      change set_attribute(:form_id, arg(:form_id))
    end

    update :mark_exception do
      require_atomic? false
      change relate_actor(:transcribed_by)
      change set_attribute(:status, :completed_exception)
      change set_attribute(:transcribe_datetime, &NaiveDateTime.local_now/0)
      change set_attribute(:exception, true)
    end

    update :mark_not_interested do
      require_atomic? false
      change relate_actor(:transcribed_by)
      change set_attribute(:status, :completed_exception)
      change set_attribute(:transcribe_datetime, &NaiveDateTime.local_now/0)
      change set_attribute(:not_interested, true)
      change set_attribute(:exception, true)
    end

    update :mark_interested do
      require_atomic? false
      change relate_actor(:transcribed_by)
      change set_attribute(:not_interested, false)
    end

    read :search do
      argument :source, :string
      argument :status, :string
      argument :time_target, :string
      argument :date_lower, :string
      argument :date_upper, :string
      argument :form_id, :uuid
      argument :actor_id, :integer

      argument :exception, :boolean do
        allow_nil? true
        default nil
      end

      pagination required?: false,
                 max_page_size: 150,
                 countable: :by_default,
                 default_limit: 50,
                 offset?: true

      prepare &Preperations.set_search_filter/2

      prepare build(load: [:previewed_by, :transcribed_by, :reviewed_by, :form, :responses])
      prepare build(load: [responses: [field: :options]])
    end
  end
end
