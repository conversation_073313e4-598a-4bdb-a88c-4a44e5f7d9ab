defmodule Admin.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @env Application.compile_env(:admin, :environment)

  @impl true
  def start(_type, _args) do
    Appsignal.Phoenix.LiveView.attach()
    children = get_children()
    Supervisor.start_link(children, strategy: :one_for_one, name: Admin.Supervisor)
  end

  @type environment :: :dev | :prod | :test
  @spec env() :: environment()
  def env(), do: @env

  @spec staging?() :: boolean()
  def staging?(), do: Application.get_env(:admin, :staging)

  def get_children do
    children = [
      # Start the Telemetry supervisor
      AdminWeb.Telemetry,
      # Start the Ecto repository
      Admin.AdminRepo,

      # Crm
      Crm.Repo,
      Crm.Repo.Replica1,
      # Project ETS cache
      Crm.Projects,
      # Area Code / NPA cache
      Admin.Crm.NPA,
      # A Registry to help orchestrate the parts of file processing.
      {Registry, keys: :unique, name: Admin.Crm.Registry},

      # Dialer
      Dialer.Landline.Repo,
      # Dialer.Landline.Repo.Replica1,
      Dialer.Wireless.Repo,
      # Dialer.Wireless.Repo.Replica1,

      # Start the endpoint when the application starts
      AdminWeb.Endpoint,
      # Start the PubSub system
      {Phoenix.PubSub, name: Admin.PubSub},
      {DNSCluster, query: Application.get_env(:admin, :dns_cluster_query) || :ignore},
      # Tracker and dynamic supervisor for sessions
      {Admin.Tracker, pubsub_server: Admin.PubSub},
      {DynamicSupervisor, name: Admin.SessionSupervisor, strategy: :one_for_one},
      FunWithFlags.Supervisor,
      # Start a worker by calling: Admin.Worker.start_link(arg)
      # {Admin.Worker, arg}
      Admin.Redis,
      # Start the Oban supervisor
      {Admin.Oban, Application.fetch_env!(:admin, Oban)}
    ]

    children =
      case staging?() do
        true ->
          children

        _ ->
          # Dev, Test, Prod
          children ++
            [
              # Start internal post-processing jobs (QC, etc)
              Admin.Jobs.Supervisor
            ]
      end

    children
  end
end
