defmodule AdminWeb.MixProject do
  use Mix.Project

  @version "1.24.0"
  def project do
    [
      app: :admin,
      version: @version,
      elixir: "~> 1.18",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      name: "Admin",
      source_url: "https://gitlab.gad-inc.com/pstallings/api_umbrella",
      homepage_url: "https://admin.gad-inc.com/",
      docs: docs(),
      preferred_cli_env: [
        "test.watch": :test
      ],
      releases: [
        admin: [
          applications: [
            fun_with_flags: :load
          ],
          steps: [:assemble, :tar],
          included_applications: [:admin],
          include_executables_for: [:unix],
          strip_beams: [keep: ["Docs"]]
        ]
      ]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Admin.Application, []},
      included_applications: [:fun_with_flags],
      extra_applications: [
        :logger,
        :runtime_tools,
        :crypto
        # :wx, # Only enabled loally during debugging, when :observer and :debugger are used.
        # :debugger,
        # :observer
      ]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(:dev), do: ["lib"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      # Use Bandit 🦝
      {:bandit, "~> 1.0"},
      # Phoenix Deps
      {:phoenix, "~> 1.7", override: true},
      {:phoenix_ecto, "~> 4.6"},
      {:phoenix_html, "~> 4.2"},
      {:phoenix_html_helpers, "~> 1.0"},
      {:phoenix_live_view, "~> 1.0"},
      {:heroicons, "~> 0.5"},
      {:phoenix_live_reload, "~> 1.3", only: :dev},
      {:phoenix_pubsub, "~> 2.0"},
      {:phoenix_view, "~> 2.0"},
      {:floki, ">= 0.33.1", only: :test},
      {:phoenix_live_dashboard, "~> 0.8"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.18"},
      {:esbuild, "~> 0.8", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.3", runtime: Mix.env() == :dev},
      {:jason, "~> 1.4"},
      {:poison, "~> 6.0"},
      {:plug_cowboy, "~> 2.5"},
      {:mint, "~> 1.0"},
      {:phx_component_helpers, "~> 1.4"},
      {:hackney, "~> 1.9"},
      {:sweet_xml, "~> 0.6"},
      {:bodyguard, "~> 2.4"},
      {:timex, "~> 3.7"},
      {:ex_phone_number, "~> 0.3"},
      {:number, "~> 1.0"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:myxql, ">= 0.0.0"},
      {:swoosh, "~> 1.14"},
      {:csv, "~> 2.4"},
      {:ex_aws, "~> 2.1"},
      {:ex_aws_s3, "~> 2.0"},
      {:temp, "~> 0.4"},
      {:gen_stage, "~> 1.1"},
      {:mix_test_watch, "~> 1.0", only: [:dev, :test], runtime: false},

      # for bcrypt
      {:comeonin, "~> 5.3"},
      {:elixir_make, "~> 0.6", runtime: false},

      # for logging
      {:appsignal, "~> 2.13"},
      {:appsignal_phoenix, "~> 2.6"},

      # for clustering
      {:dns_cluster, "~> 0.2"},

      # Let's try ash ❤️
      {:ash, "~> 3.3"},
      {:picosat_elixir, "~> 0.2"},

      # A problem was introduced in 1.3.65 where distinct can't
      # be applied after a complicated preperation.
      {:ash_postgres, "~> 2.6"},
      {:ash_phoenix, "~> 2.3"},
      {:ash_admin, "~> 0.12"},
      {:ash_state_machine, "~> 0.2.5"},
      {:ash_paper_trail, "~> 0.5"},
      {:ash_archival, "~> 1.1"},
      {:tesla, "~> 1.13"},

      # Ecto Pagination
      {:flop, "~> 0.26.1"},
      {:flop_phoenix, "~> 0.25.1"},

      # For GraphQL (Shopify Metrics)
      {:neuron, "~> 5.1.0"},

      # Scheduling
      {:oban, "~> 2.18"},
      {:oban_web, "~> 2.11"},

      # Start getting an eye on things (telemetry)
      {:telemetry, "~> 1.2"},
      {:prom_ex, "~> 1.8", only: [:dev, :test]},

      # Let's do some robust testing
      {:ex_machina, "~> 2.8", only: [:dev, :test]},
      {:stream_data, "~> 1.1"},
      {:mox, "~> 1.1"},

      # Retry phoenix storybook.
      {:phoenix_storybook, "~> 0.8"},

      # Some component gen
      {:igniter, "~> 0.6"},

      # File work
      {:explorer, "~> 0.10"},
      {:xlsxir, "~> 1.6"},
      {:rustler, "~> 0.34"},
      {:sftp_client, "~> 2.0"},

      # What do you mean I'm out of errors?
      {:ex_check, "~> 0.15.0", only: [:dev, :test], runtime: false},
      # Ex Check uses these.
      {:credo, ">= 0.0.0", only: [:dev, :test], runtime: false},
      {:ex_doc, "~> 0.30", runtime: false},
      {:dialyxir, "~> 1.2", only: [:dev, :test], runtime: false},
      {:doctor, ">= 0.0.0", only: [:dev, :test], runtime: false},
      {:sobelow, ">= 0.0.0", only: [:dev, :test], runtime: false},
      {:mix_audit, ">= 0.0.0", only: [:dev, :test], runtime: false},

      # Try out a debugger?
      {:live_debugger, "~> 0.2.2", only: [:dev]},

      # Let's web-dev correctly; or at least make Pat do it lol
      {:wallaby, "~> 0.30", only: :test, runtime: false},

      # Better env management
      {:dotenv, "~> 3.1"},

      # AMQP
      {:amqp, "~> 3.3"},

      # Mongo access
      {:mongodb_driver, "~> 1.2"},

      # Better caching
      {:redix, "~> 1.3"},

      # Panic on the call floor
      {:rexbug, ">= 2.0.0-rc1"},
      # TODO: Include when we use rabbit 2.0
      # {:observer_cli, "~> 1.8"},

      # Track office / business hours better ### TODO: Implement our own?
      {:open_hours, "~> 0.1"},

      # Flags are fun
      {:fun_with_flags, "~> 1.13", runtime: false},

      # Temporary, to fix an issue in an old version (this is required by some deps)
      {:tzdata, "~> 1.1.2"},

      # Dev utils
      {:git_ops, "~> 2.7", only: [:dev, :test]},
      {:tidewave, github: "mark-a-12/tidewave_phoenix", branch: "feat/add-tool-annotations", only: :dev}
    ]
  end

  defp docs,
    do: [
      main: "readme",
      # TODO: Get the gad logo in there
      logo: nil,
      before_closing_head_tag: &doc_head_injections/1,
      before_closing_body_tag: &doc_body_injections/1,
      extras: [
        # TODO: Add more
        "README.md",
        "CHANGELOG.md",
        "docs/live_books/ash_api_20240119.livemd",
        "docs/live_books/playground.livemd",
        "docs/live_books/sms_tests.livemd",
        "docs/live_books/sync_users.livemd",
        "docs/design/amqp-queues.md",
        "docs/design/tech-stack.md",
        "docs/IT_TOOLS.md",
        "docs/gorgias_tenantization.md",
        "docs/local_messaging_development.md",
        "docs/npa_area_code_process.md"
      ],
      groups_for_extras: [
        Design: ~r"design",
        "Live Books": ~r"live_books",
        "Process Documentation": ~r"docs"
      ]
    ]

  defp doc_head_injections(_) do
    """
    <!-- no head injections yet -->
    """
  end

  defp doc_body_injections(_) do
    # Add Mermaid support to docs
    """
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.7.0/dist/mermaid.min.js"></script>
    <script>
    document.addEventListener("DOMContentLoaded", function () {
    mermaid.initialize({
      startOnLoad: false,
      theme: document.body.className.includes("dark") ? "dark" : "default"
    });
    let id = 0;
    for (const codeEl of document.querySelectorAll("pre code.mermaid")) {
      const preEl = codeEl.parentElement;
      const graphDefinition = codeEl.textContent;
      const graphEl = document.createElement("div");
      const graphId = "mermaid-graph-" + id++;
      mermaid.render(graphId, graphDefinition).then(({svg, bindFunctions}) => {
        graphEl.innerHTML = svg;
        bindFunctions?.(graphEl);
        preEl.insertAdjacentElement("afterend", graphEl);
        preEl.remove();
      });
    }
    });
    </script>
    """
  end

  # Aliases are shortcuts or tasks specific to the current project.
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind component_gen", "esbuild component_gen"],
      "assets.deploy": [
        "tailwind default --minify",
        "tailwind storybook --minify",
        "esbuild default --minify",
        "phx.digest"
      ],
      "ecto.setup.admin": [
        "ecto.create -r Admin.AdminRepo",
        "ecto.migrate -r Admin.AdminRepo"
      ],
      "ecto.setup": [
        "ecto.setup.admin"
      ]
    ]
  end
end
